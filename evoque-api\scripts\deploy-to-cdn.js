#!/usr/bin/env node

/**
 * CDN Deployment Script for Evoque Widget
 * 
 * This script handles the deployment of the widget loader and assets to CDN
 * for production use. It includes versioning, minification, and integrity checks.
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// Configuration
const CDN_CONFIG = {
  baseUrl: 'https://cdn.evoque.digital',
  version: 'v2',
  paths: {
    widget: '../evoque-widget/dist',
    output: './cdn-dist'
  },
  files: {
    loader: 'loader.js',
    widget: 'evoque-widget.js',
    styles: 'widget.css',
    iframe: 'iframe.html'
  }
};

/**
 * Generate file integrity hash
 */
function generateIntegrity(content) {
  const hash = crypto.createHash('sha384').update(content).digest('base64');
  return `sha384-${hash}`;
}

/**
 * Minify JavaScript content (basic minification)
 */
function minifyJS(content) {
  return content
    .replace(/\/\*[\s\S]*?\*\//g, '') // Remove block comments
    .replace(/\/\/.*$/gm, '') // Remove line comments
    .replace(/\s+/g, ' ') // Collapse whitespace
    .replace(/;\s*}/g, '}') // Remove semicolons before closing braces
    .replace(/\s*{\s*/g, '{') // Clean up braces
    .replace(/\s*}\s*/g, '}')
    .replace(/\s*;\s*/g, ';')
    .trim();
}

/**
 * Minify CSS content
 */
function minifyCSS(content) {
  return content
    .replace(/\/\*[\s\S]*?\*\//g, '') // Remove comments
    .replace(/\s+/g, ' ') // Collapse whitespace
    .replace(/;\s*}/g, '}') // Remove semicolons before closing braces
    .replace(/\s*{\s*/g, '{') // Clean up braces
    .replace(/\s*}\s*/g, '}')
    .replace(/\s*;\s*/g, ';')
    .replace(/\s*,\s*/g, ',')
    .trim();
}

/**
 * Create CDN directory structure
 */
function createCDNStructure() {
  const outputDir = path.resolve(__dirname, CDN_CONFIG.paths.output);
  const versionDir = path.join(outputDir, 'widget', CDN_CONFIG.version);
  
  // Create directories
  [outputDir, path.join(outputDir, 'widget'), versionDir].forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`Created directory: ${dir}`);
    }
  });
  
  return versionDir;
}

/**
 * Process and deploy widget files
 */
function deployWidgetFiles() {
  const widgetDir = path.resolve(__dirname, CDN_CONFIG.paths.widget);
  const outputDir = createCDNStructure();
  
  console.log('Starting CDN deployment...');
  console.log(`Widget source: ${widgetDir}`);
  console.log(`CDN output: ${outputDir}`);
  
  const deploymentManifest = {
    version: CDN_CONFIG.version,
    timestamp: new Date().toISOString(),
    files: {},
    baseUrl: CDN_CONFIG.baseUrl
  };
  
  // Process each file type
  Object.entries(CDN_CONFIG.files).forEach(([type, filename]) => {
    const sourcePath = path.join(widgetDir, filename);
    
    if (!fs.existsSync(sourcePath)) {
      console.warn(`Warning: ${filename} not found at ${sourcePath}`);
      return;
    }
    
    const content = fs.readFileSync(sourcePath, 'utf8');
    let processedContent = content;
    
    // Apply minification based on file type
    if (filename.endsWith('.js')) {
      processedContent = minifyJS(content);
    } else if (filename.endsWith('.css')) {
      processedContent = minifyCSS(content);
    }
    
    // Generate integrity hash
    const integrity = generateIntegrity(processedContent);
    
    // Write minified version
    const outputPath = path.join(outputDir, filename);
    fs.writeFileSync(outputPath, processedContent);
    
    // Write development version (unminified)
    const devDir = path.join(outputDir, 'dev');
    if (!fs.existsSync(devDir)) {
      fs.mkdirSync(devDir);
    }
    fs.writeFileSync(path.join(devDir, filename), content);
    
    // Update manifest
    deploymentManifest.files[type] = {
      filename,
      size: processedContent.length,
      integrity,
      url: `${CDN_CONFIG.baseUrl}/widget/${CDN_CONFIG.version}/${filename}`,
      devUrl: `${CDN_CONFIG.baseUrl}/widget/${CDN_CONFIG.version}/dev/${filename}`
    };
    
    console.log(`✓ Processed ${filename} (${processedContent.length} bytes, integrity: ${integrity.substring(0, 20)}...)`);
  });
  
  // Generate iframe HTML for restrictive environments
  const iframeHTML = generateIframeHTML();
  const iframePath = path.join(outputDir, 'iframe.html');
  fs.writeFileSync(iframePath, iframeHTML);
  
  deploymentManifest.files.iframe = {
    filename: 'iframe.html',
    size: iframeHTML.length,
    integrity: generateIntegrity(iframeHTML),
    url: `${CDN_CONFIG.baseUrl}/widget/${CDN_CONFIG.version}/iframe.html`
  };
  
  console.log(`✓ Generated iframe.html (${iframeHTML.length} bytes)`);
  
  // Write deployment manifest
  const manifestPath = path.join(outputDir, 'manifest.json');
  fs.writeFileSync(manifestPath, JSON.stringify(deploymentManifest, null, 2));
  console.log(`✓ Generated manifest.json`);
  
  // Generate installation examples
  generateInstallationExamples(outputDir, deploymentManifest);
  
  console.log('\n🎉 CDN deployment completed successfully!');
  console.log(`\nFiles deployed to: ${outputDir}`);
  console.log(`\nProduction URLs:`);
  Object.entries(deploymentManifest.files).forEach(([type, file]) => {
    console.log(`  ${type}: ${file.url}`);
  });
  
  return deploymentManifest;
}

/**
 * Generate iframe HTML for restrictive environments
 */
function generateIframeHTML() {
  return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Evoque Widget</title>
    <style>
        body { margin: 0; padding: 0; overflow: hidden; }
        #evoque-widget-container { width: 100%; height: 100%; }
    </style>
</head>
<body>
    <div id="evoque-widget-container"></div>
    <script src="${CDN_CONFIG.baseUrl}/widget/${CDN_CONFIG.version}/evoque-widget.js"></script>
    <script>
        // Extract configuration from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const config = {
            venueId: urlParams.get('venueId'),
            primaryColor: urlParams.get('primaryColor') || '#6366F1',
            textColor: urlParams.get('textColor') || '#FFFFFF',
            position: urlParams.get('position') || 'bottom-right',
            welcomeMessage: urlParams.get('welcomeMessage') || 'Hello! How can I help you?',
            fontFamily: urlParams.get('fontFamily') || 'Inter, system-ui, sans-serif',
            fontSize: urlParams.get('fontSize') || '16px',
            buttonIcon: urlParams.get('buttonIcon') || 'chat',
            showBranding: urlParams.get('showBranding') !== 'false',
            mobileBreakpoint: parseInt(urlParams.get('mobileBreakpoint')) || 768
        };
        
        // Initialize widget in iframe mode
        if (window.EvoqueWidget && config.venueId) {
            new EvoqueWidget({
                ...config,
                mode: 'iframe',
                container: '#evoque-widget-container'
            });
        }
    </script>
</body>
</html>`;
}

/**
 * Generate installation examples and documentation
 */
function generateInstallationExamples(outputDir, manifest) {
  const examplesDir = path.join(outputDir, 'examples');
  if (!fs.existsSync(examplesDir)) {
    fs.mkdirSync(examplesDir);
  }
  
  // Basic installation example
  const basicExample = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Evoque Widget - Basic Example</title>
</head>
<body>
    <h1>Your Wedding Venue Website</h1>
    <p>This is an example of how the Evoque widget appears on your website.</p>
    
    <!-- Evoque Widget Installation -->
    <script src="${manifest.files.loader.url}"
            data-venue-id="your-venue-id"
            data-primary-color="#6366F1"
            data-welcome-message="Hello! How can I help you with your wedding venue inquiry?"
            async defer>
    </script>
    <!-- End Evoque Widget -->
</body>
</html>`;
  
  fs.writeFileSync(path.join(examplesDir, 'basic.html'), basicExample);
  
  // Advanced configuration example
  const advancedExample = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Evoque Widget - Advanced Example</title>
</head>
<body>
    <h1>Advanced Widget Configuration</h1>
    
    <!-- Evoque Widget with Full Configuration -->
    <script src="${manifest.files.loader.url}"
            data-venue-id="your-venue-id"
            data-primary-color="#8B5CF6"
            data-text-color="#FFFFFF"
            data-position="bottom-left"
            data-welcome-message="Welcome to our beautiful venue! How can we help make your special day perfect?"
            data-font-family="Georgia, serif"
            data-font-size="18px"
            data-button-icon="heart"
            data-show-branding="true"
            data-mobile-breakpoint="768"
            async defer>
    </script>
    <!-- End Evoque Widget -->
</body>
</html>`;
  
  fs.writeFileSync(path.join(examplesDir, 'advanced.html'), advancedExample);
  
  // Iframe example
  const iframeExample = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Evoque Widget - Iframe Example</title>
</head>
<body>
    <h1>Iframe Installation (for restrictive environments)</h1>
    
    <!-- Evoque Widget via Iframe -->
    <iframe src="${manifest.files.iframe.url}?venueId=your-venue-id&primaryColor=%236366F1&welcomeMessage=Hello!%20How%20can%20I%20help%20you?"
            width="60"
            height="60"
            frameborder="0"
            style="position: fixed; bottom: 20px; right: 20px; z-index: 999999; border-radius: 50%; box-shadow: 0 4px 12px rgba(0,0,0,0.15);"
            title="Evoque Wedding Venue Chat">
    </iframe>
</body>
</html>`;
  
  fs.writeFileSync(path.join(examplesDir, 'iframe.html'), iframeExample);
  
  console.log(`✓ Generated installation examples in ${examplesDir}`);
}

/**
 * Validate deployment
 */
function validateDeployment(manifest) {
  console.log('\nValidating deployment...');
  
  const outputDir = path.resolve(__dirname, CDN_CONFIG.paths.output, 'widget', CDN_CONFIG.version);
  let allValid = true;
  
  Object.entries(manifest.files).forEach(([type, file]) => {
    const filePath = path.join(outputDir, file.filename);
    
    if (!fs.existsSync(filePath)) {
      console.error(`✗ Missing file: ${file.filename}`);
      allValid = false;
      return;
    }
    
    const content = fs.readFileSync(filePath, 'utf8');
    const actualIntegrity = generateIntegrity(content);
    
    if (actualIntegrity !== file.integrity) {
      console.error(`✗ Integrity mismatch for ${file.filename}`);
      console.error(`  Expected: ${file.integrity}`);
      console.error(`  Actual: ${actualIntegrity}`);
      allValid = false;
    } else {
      console.log(`✓ ${file.filename} - integrity verified`);
    }
  });
  
  if (allValid) {
    console.log('\n✅ Deployment validation passed!');
  } else {
    console.error('\n❌ Deployment validation failed!');
    process.exit(1);
  }
}

// Main execution
if (require.main === module) {
  try {
    const manifest = deployWidgetFiles();
    validateDeployment(manifest);
    
    console.log('\n📋 Next steps:');
    console.log('1. Upload the contents of ./cdn-dist to your CDN');
    console.log('2. Update DNS/CDN configuration to point to the new files');
    console.log('3. Test the widget using the examples in ./cdn-dist/widget/v2/examples/');
    console.log('4. Update documentation with the new CDN URLs');
    
  } catch (error) {
    console.error('❌ Deployment failed:', error.message);
    process.exit(1);
  }
}

module.exports = {
  deployWidgetFiles,
  validateDeployment,
  generateIntegrity,
  CDN_CONFIG
};
