import React from 'react';
import { ArrowUpIcon, ArrowDownIcon } from '@heroicons/react/24/solid';

interface StatsCardProps {
  title: string;
  value: string | number;
  icon: React.ElementType;
  change?: number;
  trend?: 'up' | 'down' | 'neutral';
  iconColor?: string;
  trendPositive?: boolean;
  isLive?: boolean;
  showLiveIndicator?: boolean;
}

const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  icon: Icon,
  change,
  trend = 'neutral',
  iconColor = 'bg-primary-500',
  trendPositive = false,
}) => {
  // Determine if the trend is positive or negative based on the direction and trendPositive flag
  const isPositive = 
    (trend === 'up' && !trendPositive) || 
    (trend === 'down' && trendPositive);

  return (
    <div className="bg-white overflow-hidden shadow rounded-lg">
      <div className="p-5">
        <div className="flex items-center">
          <div className={`flex-shrink-0 rounded-md p-3 ${iconColor}`}>
            <Icon className="h-6 w-6 text-white" aria-hidden="true" />
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">{title}</dt>
              <dd>
                <div className="text-lg font-medium text-gray-900">{value}</div>
              </dd>
            </dl>
          </div>
        </div>
      </div>
      {change !== undefined && (
        <div className="bg-gray-50 px-5 py-3">
          <div className="flex items-center">
            {trend !== 'neutral' && (
              <>
                {trend === 'up' ? (
                  <ArrowUpIcon
                    className={`h-5 w-5 ${
                      isPositive ? 'text-success-500' : 'text-danger-500'
                    }`}
                    aria-hidden="true"
                  />
                ) : (
                  <ArrowDownIcon
                    className={`h-5 w-5 ${
                      isPositive ? 'text-danger-500' : 'text-success-500'
                    }`}
                    aria-hidden="true"
                  />
                )}
              </>
            )}
            <span
              className={`text-sm ${
                trend === 'neutral'
                  ? 'text-gray-500'
                  : isPositive
                  ? 'text-success-500'
                  : 'text-danger-500'
              } ml-1`}
            >
              <span className="font-medium">
                {change > 0 ? '+' : ''}
                {change}%
              </span>{' '}
              <span className="hidden sm:inline">from previous period</span>
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default StatsCard;