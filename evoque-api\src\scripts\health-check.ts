#!/usr/bin/env ts-node

/**
 * Comprehensive Health Check Script
 * 
 * Validates all system components and external dependencies
 * for production readiness and ongoing monitoring.
 */

import { PrismaClient } from '@prisma/client';
import fetch from 'node-fetch';
import { WebSocketService } from '../services/websocket';
import { NotificationService } from '../services/notifications';

const prisma = new PrismaClient();

interface HealthCheckResult {
  component: string;
  status: 'healthy' | 'warning' | 'critical';
  message: string;
  responseTime?: number;
  details?: any;
}

interface HealthReport {
  overall: 'healthy' | 'warning' | 'critical';
  timestamp: string;
  checks: HealthCheckResult[];
  summary: {
    healthy: number;
    warning: number;
    critical: number;
    total: number;
  };
}

/**
 * Check database connectivity and performance
 */
async function checkDatabase(): Promise<HealthCheckResult> {
  const startTime = Date.now();
  
  try {
    // Test basic connectivity
    await prisma.$connect();
    
    // Test query performance
    await prisma.$queryRaw`SELECT 1`;
    
    // Check table accessibility
    const userCount = await prisma.user.count();
    const venueCount = await prisma.venue.count();
    const inquiryCount = await prisma.inquiry.count();
    
    const responseTime = Date.now() - startTime;
    
    if (responseTime > 1000) {
      return {
        component: 'Database',
        status: 'warning',
        message: `Database responding slowly (${responseTime}ms)`,
        responseTime,
        details: { userCount, venueCount, inquiryCount }
      };
    }
    
    return {
      component: 'Database',
      status: 'healthy',
      message: 'Database connection healthy',
      responseTime,
      details: { userCount, venueCount, inquiryCount }
    };
  } catch (error) {
    return {
      component: 'Database',
      status: 'critical',
      message: `Database connection failed: ${error.message}`,
      responseTime: Date.now() - startTime
    };
  }
}

/**
 * Check GraphQL API health
 */
async function checkGraphQLAPI(): Promise<HealthCheckResult> {
  const startTime = Date.now();
  
  try {
    const apiUrl = process.env.NEXT_PUBLIC_GRAPHQL_URL || 'http://localhost:4000/graphql';
    
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: '{ __typename }'
      }),
      timeout: 5000
    });
    
    const responseTime = Date.now() - startTime;
    
    if (!response.ok) {
      return {
        component: 'GraphQL API',
        status: 'critical',
        message: `GraphQL API returned ${response.status}`,
        responseTime
      };
    }
    
    const data = await response.json();
    
    if (data.errors) {
      return {
        component: 'GraphQL API',
        status: 'warning',
        message: 'GraphQL API has errors',
        responseTime,
        details: data.errors
      };
    }
    
    return {
      component: 'GraphQL API',
      status: 'healthy',
      message: 'GraphQL API responding normally',
      responseTime
    };
  } catch (error) {
    return {
      component: 'GraphQL API',
      status: 'critical',
      message: `GraphQL API check failed: ${error.message}`,
      responseTime: Date.now() - startTime
    };
  }
}

/**
 * Check external service dependencies
 */
async function checkExternalServices(): Promise<HealthCheckResult[]> {
  const results: HealthCheckResult[] = [];
  
  // Check Firebase (Push Notifications)
  if (process.env.FIREBASE_SERVER_KEY) {
    const startTime = Date.now();
    try {
      const response = await fetch('https://fcm.googleapis.com/fcm/send', {
        method: 'POST',
        headers: {
          'Authorization': `key=${process.env.FIREBASE_SERVER_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          to: 'test-token',
          notification: { title: 'Health Check', body: 'Test' }
        }),
        timeout: 5000
      });
      
      const responseTime = Date.now() - startTime;
      
      // FCM returns 400 for invalid token, which is expected for health check
      if (response.status === 400 || response.status === 200) {
        results.push({
          component: 'Firebase FCM',
          status: 'healthy',
          message: 'Firebase FCM service accessible',
          responseTime
        });
      } else {
        results.push({
          component: 'Firebase FCM',
          status: 'warning',
          message: `Firebase FCM returned ${response.status}`,
          responseTime
        });
      }
    } catch (error) {
      results.push({
        component: 'Firebase FCM',
        status: 'critical',
        message: `Firebase FCM check failed: ${error.message}`,
        responseTime: Date.now() - startTime
      });
    }
  } else {
    results.push({
      component: 'Firebase FCM',
      status: 'warning',
      message: 'Firebase FCM not configured (FIREBASE_SERVER_KEY missing)'
    });
  }
  
  // Check Twilio (SMS)
  if (process.env.TWILIO_ACCOUNT_SID && process.env.TWILIO_AUTH_TOKEN) {
    const startTime = Date.now();
    try {
      const auth = Buffer.from(`${process.env.TWILIO_ACCOUNT_SID}:${process.env.TWILIO_AUTH_TOKEN}`).toString('base64');
      const response = await fetch(`https://api.twilio.com/2010-04-01/Accounts/${process.env.TWILIO_ACCOUNT_SID}.json`, {
        headers: {
          'Authorization': `Basic ${auth}`
        },
        timeout: 5000
      });
      
      const responseTime = Date.now() - startTime;
      
      if (response.ok) {
        results.push({
          component: 'Twilio SMS',
          status: 'healthy',
          message: 'Twilio SMS service accessible',
          responseTime
        });
      } else {
        results.push({
          component: 'Twilio SMS',
          status: 'critical',
          message: `Twilio SMS returned ${response.status}`,
          responseTime
        });
      }
    } catch (error) {
      results.push({
        component: 'Twilio SMS',
        status: 'critical',
        message: `Twilio SMS check failed: ${error.message}`,
        responseTime: Date.now() - startTime
      });
    }
  } else {
    results.push({
      component: 'Twilio SMS',
      status: 'warning',
      message: 'Twilio SMS not configured (credentials missing)'
    });
  }
  
  // Check Resend (Email)
  if (process.env.RESEND_API_KEY) {
    const startTime = Date.now();
    try {
      const response = await fetch('https://api.resend.com/domains', {
        headers: {
          'Authorization': `Bearer ${process.env.RESEND_API_KEY}`
        },
        timeout: 5000
      });
      
      const responseTime = Date.now() - startTime;
      
      if (response.ok) {
        results.push({
          component: 'Resend Email',
          status: 'healthy',
          message: 'Resend email service accessible',
          responseTime
        });
      } else {
        results.push({
          component: 'Resend Email',
          status: 'critical',
          message: `Resend email returned ${response.status}`,
          responseTime
        });
      }
    } catch (error) {
      results.push({
        component: 'Resend Email',
        status: 'critical',
        message: `Resend email check failed: ${error.message}`,
        responseTime: Date.now() - startTime
      });
    }
  } else {
    results.push({
      component: 'Resend Email',
      status: 'warning',
      message: 'Resend email not configured (RESEND_API_KEY missing)'
    });
  }
  
  return results;
}

/**
 * Check CDN and static assets
 */
async function checkCDN(): Promise<HealthCheckResult> {
  const startTime = Date.now();
  
  try {
    const cdnUrl = process.env.WIDGET_CDN_URL || 'https://cdn.evoque.digital/widget/v2';
    const loaderUrl = `${cdnUrl}/loader.js`;
    
    const response = await fetch(loaderUrl, {
      method: 'HEAD',
      timeout: 5000
    });
    
    const responseTime = Date.now() - startTime;
    
    if (!response.ok) {
      return {
        component: 'CDN',
        status: 'critical',
        message: `CDN returned ${response.status} for widget loader`,
        responseTime
      };
    }
    
    return {
      component: 'CDN',
      status: 'healthy',
      message: 'CDN serving widget assets correctly',
      responseTime
    };
  } catch (error) {
    return {
      component: 'CDN',
      status: 'critical',
      message: `CDN check failed: ${error.message}`,
      responseTime: Date.now() - startTime
    };
  }
}

/**
 * Check system resources
 */
async function checkSystemResources(): Promise<HealthCheckResult[]> {
  const results: HealthCheckResult[] = [];
  
  try {
    const memUsage = process.memoryUsage();
    const memUsedMB = Math.round(memUsage.heapUsed / 1024 / 1024);
    const memTotalMB = Math.round(memUsage.heapTotal / 1024 / 1024);
    
    if (memUsedMB > 500) {
      results.push({
        component: 'Memory Usage',
        status: 'warning',
        message: `High memory usage: ${memUsedMB}MB`,
        details: { used: memUsedMB, total: memTotalMB }
      });
    } else {
      results.push({
        component: 'Memory Usage',
        status: 'healthy',
        message: `Memory usage normal: ${memUsedMB}MB`,
        details: { used: memUsedMB, total: memTotalMB }
      });
    }
    
    // Check uptime
    const uptimeSeconds = process.uptime();
    const uptimeHours = Math.round(uptimeSeconds / 3600 * 100) / 100;
    
    results.push({
      component: 'Process Uptime',
      status: 'healthy',
      message: `Process running for ${uptimeHours} hours`,
      details: { uptimeSeconds, uptimeHours }
    });
    
  } catch (error) {
    results.push({
      component: 'System Resources',
      status: 'warning',
      message: `Could not check system resources: ${error.message}`
    });
  }
  
  return results;
}

/**
 * Run comprehensive health check
 */
async function runHealthCheck(): Promise<HealthReport> {
  console.log('🏥 Running comprehensive health check...\n');
  
  const checks: HealthCheckResult[] = [];
  
  // Core system checks
  checks.push(await checkDatabase());
  checks.push(await checkGraphQLAPI());
  checks.push(await checkCDN());
  
  // External service checks
  const externalChecks = await checkExternalServices();
  checks.push(...externalChecks);
  
  // System resource checks
  const systemChecks = await checkSystemResources();
  checks.push(...systemChecks);
  
  // Calculate summary
  const summary = {
    healthy: checks.filter(c => c.status === 'healthy').length,
    warning: checks.filter(c => c.status === 'warning').length,
    critical: checks.filter(c => c.status === 'critical').length,
    total: checks.length
  };
  
  // Determine overall status
  let overall: 'healthy' | 'warning' | 'critical' = 'healthy';
  if (summary.critical > 0) {
    overall = 'critical';
  } else if (summary.warning > 0) {
    overall = 'warning';
  }
  
  const report: HealthReport = {
    overall,
    timestamp: new Date().toISOString(),
    checks,
    summary
  };
  
  return report;
}

/**
 * Display health report
 */
function displayHealthReport(report: HealthReport): void {
  console.log('📊 Health Check Report');
  console.log('='.repeat(50));
  console.log(`Overall Status: ${getStatusEmoji(report.overall)} ${report.overall.toUpperCase()}`);
  console.log(`Timestamp: ${report.timestamp}`);
  console.log(`Summary: ${report.summary.healthy} healthy, ${report.summary.warning} warnings, ${report.summary.critical} critical\n`);
  
  // Group checks by status
  const criticalChecks = report.checks.filter(c => c.status === 'critical');
  const warningChecks = report.checks.filter(c => c.status === 'warning');
  const healthyChecks = report.checks.filter(c => c.status === 'healthy');
  
  if (criticalChecks.length > 0) {
    console.log('🚨 CRITICAL ISSUES:');
    criticalChecks.forEach(check => {
      console.log(`   ${check.component}: ${check.message}`);
      if (check.responseTime) console.log(`      Response time: ${check.responseTime}ms`);
    });
    console.log();
  }
  
  if (warningChecks.length > 0) {
    console.log('⚠️  WARNINGS:');
    warningChecks.forEach(check => {
      console.log(`   ${check.component}: ${check.message}`);
      if (check.responseTime) console.log(`      Response time: ${check.responseTime}ms`);
    });
    console.log();
  }
  
  if (healthyChecks.length > 0) {
    console.log('✅ HEALTHY COMPONENTS:');
    healthyChecks.forEach(check => {
      console.log(`   ${check.component}: ${check.message}`);
      if (check.responseTime) console.log(`      Response time: ${check.responseTime}ms`);
    });
  }
}

function getStatusEmoji(status: string): string {
  switch (status) {
    case 'healthy': return '✅';
    case 'warning': return '⚠️';
    case 'critical': return '🚨';
    default: return '❓';
  }
}

// CLI interface
if (require.main === module) {
  const args = process.argv.slice(2);
  const jsonOutput = args.includes('--json');
  const exitOnError = args.includes('--exit-on-error');
  
  runHealthCheck()
    .then(report => {
      if (jsonOutput) {
        console.log(JSON.stringify(report, null, 2));
      } else {
        displayHealthReport(report);
      }
      
      if (exitOnError && report.overall === 'critical') {
        process.exit(1);
      } else {
        process.exit(0);
      }
    })
    .catch(error => {
      console.error('💥 Health check failed:', error);
      process.exit(1);
    })
    .finally(() => {
      prisma.$disconnect();
    });
}

export { runHealthCheck, HealthReport, HealthCheckResult };
