{"name": "evoque-api", "version": "1.0.0", "description": "Backend API for the Evoque.Digital platform, an AI-powered wedding venue management system", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "nodemon --exec ts-node src/index.ts", "build": "tsc", "lint": "eslint . --ext .ts", "format": "prettier --write \"src/**/*.ts\"", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio", "seed": "ts-node prisma/seed.ts", "validate": "ts-node src/scripts/validate-implementation.ts", "validate:platform": "ts-node src/scripts/validate-platform-detection.ts", "validate:analytics": "ts-node src/scripts/validate-analytics.ts", "deploy:staging": "npm run build && npm run validate && echo 'Ready for staging deployment'", "deploy:production": "npm run build && npm run validate && npm run test && echo 'Ready for production deployment'", "db:migrate": "ts-node src/scripts/migrate-database.ts", "db:seed": "ts-node src/scripts/seed-database.ts", "health:check": "ts-node src/scripts/health-check.ts", "cdn:deploy": "npm run build && node scripts/deploy-to-cdn.js"}, "keywords": ["wedding", "venue", "management", "ai", "graphql", "api"], "author": "Evoque.Digital", "license": "UNLICENSED", "private": true, "dependencies": {"@apollo/server": "^4.9.5", "@graphql-tools/schema": "^10.0.2", "@graphql-tools/utils": "^10.0.11", "@prisma/client": "^5.7.1", "apollo-server-core": "^3.13.0", "apollo-server-express": "^3.13.0", "axios": "^1.9.0", "bcrypt": "^5.1.1", "cheerio": "^1.0.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "graphql": "^16.8.1", "graphql-scalars": "^1.22.4", "graphql-subscriptions": "^2.0.0", "graphql-type-json": "^0.3.2", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "openai": "^4.24.1", "subscriptions-transport-ws": "^0.11.0"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.11", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.17.46", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.2", "jest": "^29.7.0", "nodemon": "^3.0.2", "prettier": "^3.1.1", "prisma": "^5.7.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}}