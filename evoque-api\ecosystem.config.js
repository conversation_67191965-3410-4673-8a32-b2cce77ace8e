/**
 * PM2 Ecosystem Configuration for Evoque Platform
 * 
 * This configuration file defines how PM2 should manage the Evoque API
 * in different environments (development, staging, production).
 */

module.exports = {
  apps: [
    {
      name: 'evoque-api',
      script: './dist/index.js',
      instances: 'max', // Use all available CPU cores
      exec_mode: 'cluster',
      
      // Environment variables
      env: {
        NODE_ENV: 'development',
        PORT: 4000,
        LOG_LEVEL: 'debug'
      },
      
      env_staging: {
        NODE_ENV: 'staging',
        PORT: 4000,
        LOG_LEVEL: 'info'
      },
      
      env_production: {
        NODE_ENV: 'production',
        PORT: 4000,
        LOG_LEVEL: 'warn'
      },
      
      // Logging
      log_file: './logs/combined.log',
      out_file: './logs/out.log',
      error_file: './logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // Process management
      max_memory_restart: '1G',
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s',
      
      // Monitoring
      monitoring: true,
      pmx: true,
      
      // Health checks
      health_check_grace_period: 3000,
      health_check_fatal_exceptions: true,
      
      // Auto restart on file changes (development only)
      watch: false,
      ignore_watch: ['node_modules', 'logs', 'dist'],
      
      // Graceful shutdown
      kill_timeout: 5000,
      listen_timeout: 3000,
      
      // Source map support
      source_map_support: true,
      
      // Merge logs from all instances
      merge_logs: true,
      
      // Time zone
      time: true,
      
      // Auto restart on memory limit
      max_memory_restart: '1G',
      
      // Cron restart (optional - restart daily at 3 AM)
      cron_restart: '0 3 * * *',
      
      // Environment-specific overrides
      env_file: '.env'
    }
  ],
  
  // Deployment configuration
  deploy: {
    production: {
      user: 'deploy',
      host: ['api.evoque.digital'],
      ref: 'origin/main',
      repo: '**************:your-org/evoque-platform.git',
      path: '/var/www/evoque-api',
      'pre-deploy-local': '',
      'post-deploy': 'npm ci --only=production && npm run build && npm run production:setup && pm2 reload ecosystem.config.js --env production',
      'pre-setup': 'apt update && apt install git -y'
    },
    
    staging: {
      user: 'deploy',
      host: ['staging-api.evoque.digital'],
      ref: 'origin/develop',
      repo: '**************:your-org/evoque-platform.git',
      path: '/var/www/evoque-api-staging',
      'post-deploy': 'npm ci && npm run build && npm run production:setup && pm2 reload ecosystem.config.js --env staging'
    }
  },
  
  // PM2+ monitoring configuration
  pmx: {
    http: true,
    ignore_routes: ['/health', '/metrics'],
    errors: true,
    custom_probes: true,
    network: true,
    ports: true
  }
};

/**
 * Usage Examples:
 * 
 * Development:
 * pm2 start ecosystem.config.js
 * 
 * Staging:
 * pm2 start ecosystem.config.js --env staging
 * 
 * Production:
 * pm2 start ecosystem.config.js --env production
 * 
 * Deployment:
 * pm2 deploy ecosystem.config.js production setup
 * pm2 deploy ecosystem.config.js production
 * 
 * Monitoring:
 * pm2 monit
 * pm2 logs evoque-api
 * pm2 show evoque-api
 * 
 * Management:
 * pm2 restart evoque-api
 * pm2 reload evoque-api
 * pm2 stop evoque-api
 * pm2 delete evoque-api
 * 
 * Health checks:
 * pm2 ping evoque-api
 * npm run health:check
 */
