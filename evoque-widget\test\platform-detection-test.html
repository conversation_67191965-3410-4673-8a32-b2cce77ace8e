<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Platform Detection Test Suite</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0 0 10px 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header p {
            margin: 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .content {
            padding: 30px;
        }
        
        .test-section {
            margin-bottom: 40px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .test-section-header {
            background: #f7fafc;
            padding: 20px;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .test-section-header h2 {
            margin: 0 0 10px 0;
            color: #2d3748;
            font-size: 1.5em;
        }
        
        .test-section-header p {
            margin: 0;
            color: #718096;
        }
        
        .test-section-content {
            padding: 20px;
        }
        
        .platform-test {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .platform-card {
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .platform-card:hover {
            border-color: #667eea;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
        }
        
        .platform-card h3 {
            margin: 0 0 15px 0;
            color: #2d3748;
        }
        
        .test-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background 0.3s ease;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #5a67d8;
        }
        
        .test-button.secondary {
            background: #718096;
        }
        
        .test-button.secondary:hover {
            background: #4a5568;
        }
        
        .test-result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 6px;
            font-weight: 500;
        }
        
        .test-result.success {
            background: #f0fff4;
            color: #22543d;
            border: 1px solid #9ae6b4;
        }
        
        .test-result.error {
            background: #fed7d7;
            color: #742a2a;
            border: 1px solid #feb2b2;
        }
        
        .test-result.pending {
            background: #fefcbf;
            color: #744210;
            border: 1px solid #f6e05e;
        }
        
        .log-section {
            background: #1a202c;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 20px;
        }
        
        .log-entry {
            margin-bottom: 8px;
            padding: 4px 0;
        }
        
        .log-entry.info { color: #63b3ed; }
        .log-entry.success { color: #68d391; }
        .log-entry.error { color: #fc8181; }
        .log-entry.warn { color: #f6ad55; }
        
        .current-detection {
            background: #edf2f7;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .current-detection h3 {
            margin: 0 0 15px 0;
            color: #2d3748;
        }
        
        .detection-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .detection-item {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
        }
        
        .detection-item strong {
            display: block;
            color: #4a5568;
            margin-bottom: 5px;
        }
        
        @media (max-width: 768px) {
            .platform-test {
                grid-template-columns: 1fr;
            }
            
            .detection-info {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Platform Detection Test Suite</h1>
            <p>Validate widget platform detection accuracy and reliability</p>
        </div>
        
        <div class="content">
            <div class="current-detection">
                <h3>Current Environment Detection</h3>
                <div class="detection-info" id="current-detection">
                    <div class="detection-item">
                        <strong>Platform:</strong>
                        <span id="detected-platform">Detecting...</span>
                    </div>
                    <div class="detection-item">
                        <strong>Hostname:</strong>
                        <span id="detected-hostname">-</span>
                    </div>
                    <div class="detection-item">
                        <strong>Body Classes:</strong>
                        <span id="detected-body-classes">-</span>
                    </div>
                    <div class="detection-item">
                        <strong>Meta Generator:</strong>
                        <span id="detected-meta">-</span>
                    </div>
                </div>
            </div>

            <div class="test-section">
                <div class="test-section-header">
                    <h2>Platform Simulation Tests</h2>
                    <p>Test detection accuracy by simulating different platform environments</p>
                </div>
                <div class="test-section-content">
                    <div class="platform-test">
                        <div class="platform-card">
                            <h3>🔧 WordPress</h3>
                            <button class="test-button" onclick="simulateWordPress()">Simulate WordPress</button>
                            <div class="test-result" id="wordpress-result">Ready to test</div>
                        </div>
                        
                        <div class="platform-card">
                            <h3>🎨 Wix</h3>
                            <button class="test-button" onclick="simulateWix()">Simulate Wix</button>
                            <div class="test-result" id="wix-result">Ready to test</div>
                        </div>
                        
                        <div class="platform-card">
                            <h3>⬜ Squarespace</h3>
                            <button class="test-button" onclick="simulateSquarespace()">Simulate Squarespace</button>
                            <div class="test-result" id="squarespace-result">Ready to test</div>
                        </div>
                        
                        <div class="platform-card">
                            <h3>🛒 Shopify</h3>
                            <button class="test-button" onclick="simulateShopify()">Simulate Shopify</button>
                            <div class="test-result" id="shopify-result">Ready to test</div>
                        </div>
                        
                        <div class="platform-card">
                            <h3>🌊 Webflow</h3>
                            <button class="test-button" onclick="simulateWebflow()">Simulate Webflow</button>
                            <div class="test-result" id="webflow-result">Ready to test</div>
                        </div>
                        
                        <div class="platform-card">
                            <h3>📱 Weebly</h3>
                            <button class="test-button" onclick="simulateWeebly()">Simulate Weebly</button>
                            <div class="test-result" id="weebly-result">Ready to test</div>
                        </div>
                    </div>
                    
                    <div style="text-align: center; margin-top: 30px;">
                        <button class="test-button" onclick="runAllTests()">🚀 Run All Tests</button>
                        <button class="test-button secondary" onclick="resetTests()">🔄 Reset Tests</button>
                        <button class="test-button secondary" onclick="clearLogs()">🗑️ Clear Logs</button>
                    </div>
                </div>
            </div>

            <div class="test-section">
                <div class="test-section-header">
                    <h2>Test Logs</h2>
                    <p>Detailed logging of detection tests and results</p>
                </div>
                <div class="test-section-content">
                    <div class="log-section" id="test-logs">
                        <div class="log-entry info">[INFO] Platform detection test suite initialized</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Mock the EvoqueWidgetLoader for testing
        class MockEvoqueWidgetLoader {
            constructor(config = {}) {
                this.config = config;
                this.platform = this.detectPlatform();
            }

            detectPlatform() {
                const hostname = window.location.hostname.toLowerCase();
                const bodyClasses = document.body.className.toLowerCase();
                const htmlClasses = document.documentElement.className.toLowerCase();
                
                const metaTags = Array.from(document.querySelectorAll('meta[name="generator"]'))
                    .map(meta => meta.content.toLowerCase());

                // WordPress detection
                if (this.isWordPress(hostname, bodyClasses, htmlClasses, metaTags)) {
                    return { name: 'wordpress', detected: true, optimizations: ['shadow-dom', 'namespace-isolation'] };
                }

                // Wix detection
                if (this.isWix(hostname, metaTags)) {
                    return { name: 'wix', detected: true, optimizations: ['iframe-fallback', 'wix-specific-positioning'] };
                }

                // Squarespace detection
                if (this.isSquarespace(bodyClasses, htmlClasses, metaTags)) {
                    return { name: 'squarespace', detected: true, optimizations: ['squarespace-css-reset'] };
                }

                // Shopify detection
                if (this.isShopify(metaTags)) {
                    return { name: 'shopify', detected: true, optimizations: ['shopify-theme-compat'] };
                }

                // Webflow detection
                if (this.isWebflow(bodyClasses, htmlClasses)) {
                    return { name: 'webflow', detected: true, optimizations: ['webflow-interactions'] };
                }

                // Weebly detection
                if (this.isWeebly(hostname, metaTags)) {
                    return { name: 'weebly', detected: true, optimizations: ['weebly-css-reset'] };
                }

                return { name: 'generic', detected: false, optimizations: ['standard-positioning'] };
            }

            isWordPress(hostname, bodyClasses, htmlClasses, metaTags) {
                return metaTags.some(content => content.includes('wordpress')) ||
                       document.querySelector('link[href*="wp-content"]') ||
                       ['wordpress', 'wp-', 'home', 'blog'].some(cls => bodyClasses.includes(cls)) ||
                       document.querySelector('#wpadminbar');
            }

            isWix(hostname, metaTags) {
                return hostname.includes('wixsite.com') ||
                       hostname.includes('wix.com') ||
                       metaTags.some(content => content.includes('wix')) ||
                       document.querySelector('[data-wix-editor]');
            }

            isSquarespace(bodyClasses, htmlClasses, metaTags) {
                return metaTags.some(content => content.includes('squarespace')) ||
                       bodyClasses.includes('squarespace') ||
                       bodyClasses.includes('sqs-') ||
                       document.querySelector('.sqs-block');
            }

            isShopify(metaTags) {
                return window.Shopify ||
                       metaTags.some(content => content.includes('shopify')) ||
                       document.querySelector('script[src*="shopify"]') ||
                       document.querySelector('.shopify-section');
            }

            isWebflow(bodyClasses, htmlClasses) {
                return bodyClasses.includes('w-') ||
                       htmlClasses.includes('w-') ||
                       document.querySelector('script[src*="webflow"]') ||
                       document.querySelector('.w-container');
            }

            isWeebly(hostname, metaTags) {
                return hostname.includes('weebly.com') ||
                       metaTags.some(content => content.includes('weebly')) ||
                       document.querySelector('#weebly-footer');
            }

            getPlatform() {
                return this.platform;
            }
        }

        let testResults = {};

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = `[${timestamp}] [${type.toUpperCase()}] ${message}`;
            
            const logsContainer = document.getElementById('test-logs');
            logsContainer.appendChild(logEntry);
            logsContainer.scrollTop = logsContainer.scrollHeight;
        }

        function updateCurrentDetection() {
            const loader = new MockEvoqueWidgetLoader();
            const platform = loader.getPlatform();
            
            document.getElementById('detected-platform').textContent = 
                `${platform.name} (${platform.detected ? 'detected' : 'generic'})`;
            document.getElementById('detected-hostname').textContent = window.location.hostname;
            document.getElementById('detected-body-classes').textContent = 
                document.body.className.substring(0, 50) + (document.body.className.length > 50 ? '...' : '');
            
            const metaTags = Array.from(document.querySelectorAll('meta[name="generator"]'))
                .map(meta => meta.content);
            document.getElementById('detected-meta').textContent = 
                metaTags.length > 0 ? metaTags.join(', ') : 'None';
        }

        function updateTestResult(platform, success, message) {
            const resultElement = document.getElementById(`${platform}-result`);
            resultElement.className = `test-result ${success ? 'success' : 'error'}`;
            resultElement.textContent = message;
            testResults[platform] = { success, message };
        }

        function simulateWordPress() {
            log('Simulating WordPress environment...', 'info');
            
            // Add WordPress meta tag
            const meta = document.createElement('meta');
            meta.name = 'generator';
            meta.content = 'WordPress 6.3';
            document.head.appendChild(meta);
            
            // Add WordPress body class
            document.body.className += ' wordpress wp-custom-logo';
            
            // Test detection
            const loader = new MockEvoqueWidgetLoader();
            const platform = loader.getPlatform();
            
            const success = platform.name === 'wordpress' && platform.detected;
            updateTestResult('wordpress', success, 
                success ? '✅ WordPress detected correctly' : '❌ WordPress detection failed');
            
            log(`WordPress test result: ${success ? 'PASS' : 'FAIL'}`, success ? 'success' : 'error');
            updateCurrentDetection();
        }

        function simulateWix() {
            log('Simulating Wix environment...', 'info');
            
            // Add Wix meta tag
            const meta = document.createElement('meta');
            meta.name = 'generator';
            meta.content = 'Wix.com Website Builder';
            document.head.appendChild(meta);
            
            // Add Wix element
            const wixElement = document.createElement('div');
            wixElement.setAttribute('data-wix-editor', 'true');
            document.body.appendChild(wixElement);
            
            // Test detection
            const loader = new MockEvoqueWidgetLoader();
            const platform = loader.getPlatform();
            
            const success = platform.name === 'wix' && platform.detected;
            updateTestResult('wix', success, 
                success ? '✅ Wix detected correctly' : '❌ Wix detection failed');
            
            log(`Wix test result: ${success ? 'PASS' : 'FAIL'}`, success ? 'success' : 'error');
            updateCurrentDetection();
        }

        function simulateSquarespace() {
            log('Simulating Squarespace environment...', 'info');
            
            // Add Squarespace meta tag
            const meta = document.createElement('meta');
            meta.name = 'generator';
            meta.content = 'Squarespace';
            document.head.appendChild(meta);
            
            // Add Squarespace body class
            document.body.className += ' squarespace sqs-site-loaded';
            
            // Test detection
            const loader = new MockEvoqueWidgetLoader();
            const platform = loader.getPlatform();
            
            const success = platform.name === 'squarespace' && platform.detected;
            updateTestResult('squarespace', success, 
                success ? '✅ Squarespace detected correctly' : '❌ Squarespace detection failed');
            
            log(`Squarespace test result: ${success ? 'PASS' : 'FAIL'}`, success ? 'success' : 'error');
            updateCurrentDetection();
        }

        function simulateShopify() {
            log('Simulating Shopify environment...', 'info');
            
            // Add Shopify global object
            window.Shopify = { shop: 'test-shop.myshopify.com' };
            
            // Add Shopify meta tag
            const meta = document.createElement('meta');
            meta.name = 'generator';
            meta.content = 'Shopify';
            document.head.appendChild(meta);
            
            // Test detection
            const loader = new MockEvoqueWidgetLoader();
            const platform = loader.getPlatform();
            
            const success = platform.name === 'shopify' && platform.detected;
            updateTestResult('shopify', success, 
                success ? '✅ Shopify detected correctly' : '❌ Shopify detection failed');
            
            log(`Shopify test result: ${success ? 'PASS' : 'FAIL'}`, success ? 'success' : 'error');
            updateCurrentDetection();
        }

        function simulateWebflow() {
            log('Simulating Webflow environment...', 'info');
            
            // Add Webflow body classes
            document.body.className += ' w-body';
            
            // Add Webflow element
            const webflowElement = document.createElement('div');
            webflowElement.className = 'w-container';
            document.body.appendChild(webflowElement);
            
            // Test detection
            const loader = new MockEvoqueWidgetLoader();
            const platform = loader.getPlatform();
            
            const success = platform.name === 'webflow' && platform.detected;
            updateTestResult('webflow', success, 
                success ? '✅ Webflow detected correctly' : '❌ Webflow detection failed');
            
            log(`Webflow test result: ${success ? 'PASS' : 'FAIL'}`, success ? 'success' : 'error');
            updateCurrentDetection();
        }

        function simulateWeebly() {
            log('Simulating Weebly environment...', 'info');
            
            // Add Weebly meta tag
            const meta = document.createElement('meta');
            meta.name = 'generator';
            meta.content = 'Weebly';
            document.head.appendChild(meta);
            
            // Add Weebly element
            const weeblyElement = document.createElement('div');
            weeblyElement.id = 'weebly-footer';
            document.body.appendChild(weeblyElement);
            
            // Test detection
            const loader = new MockEvoqueWidgetLoader();
            const platform = loader.getPlatform();
            
            const success = platform.name === 'weebly' && platform.detected;
            updateTestResult('weebly', success, 
                success ? '✅ Weebly detected correctly' : '❌ Weebly detection failed');
            
            log(`Weebly test result: ${success ? 'PASS' : 'FAIL'}`, success ? 'success' : 'error');
            updateCurrentDetection();
        }

        function runAllTests() {
            log('Running all platform detection tests...', 'info');
            
            resetTests();
            
            setTimeout(() => simulateWordPress(), 100);
            setTimeout(() => simulateWix(), 200);
            setTimeout(() => simulateSquarespace(), 300);
            setTimeout(() => simulateShopify(), 400);
            setTimeout(() => simulateWebflow(), 500);
            setTimeout(() => simulateWeebly(), 600);
            
            setTimeout(() => {
                const totalTests = Object.keys(testResults).length;
                const passedTests = Object.values(testResults).filter(r => r.success).length;
                log(`All tests completed: ${passedTests}/${totalTests} passed`, 
                    passedTests === totalTests ? 'success' : 'error');
            }, 700);
        }

        function resetTests() {
            log('Resetting test environment...', 'info');
            
            // Remove added meta tags
            document.querySelectorAll('meta[name="generator"]').forEach(meta => {
                if (['WordPress', 'Wix', 'Squarespace', 'Shopify', 'Weebly'].some(platform => 
                    meta.content.includes(platform))) {
                    meta.remove();
                }
            });
            
            // Reset body classes
            document.body.className = document.body.className
                .replace(/wordpress|wp-custom-logo|squarespace|sqs-site-loaded|w-body/g, '')
                .trim();
            
            // Remove added elements
            document.querySelectorAll('[data-wix-editor], .w-container, #weebly-footer').forEach(el => el.remove());
            
            // Remove Shopify global
            if (window.Shopify) delete window.Shopify;
            
            // Reset test results
            testResults = {};
            ['wordpress', 'wix', 'squarespace', 'shopify', 'webflow', 'weebly'].forEach(platform => {
                const resultElement = document.getElementById(`${platform}-result`);
                resultElement.className = 'test-result';
                resultElement.textContent = 'Ready to test';
            });
            
            updateCurrentDetection();
            log('Test environment reset complete', 'success');
        }

        function clearLogs() {
            const logsContainer = document.getElementById('test-logs');
            logsContainer.innerHTML = '<div class="log-entry info">[INFO] Logs cleared - ready for new tests</div>';
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            updateCurrentDetection();
            log('Platform detection test suite ready', 'success');
        });
    </script>
</body>
</html>
