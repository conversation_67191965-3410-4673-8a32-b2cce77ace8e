import React, { useState, useEffect } from 'react';
import styled, { ThemeProvider } from 'styled-components';
import { WidgetOptions } from '../types';
import { generateEmbedCode } from '../utils/embedCode';
import Widget from './Widget';

interface InstallationWizardProps {
  onComplete?: (config: WidgetOptions) => void;
  onClose?: () => void;
}

interface WizardStep {
  id: string;
  title: string;
  description: string;
  component: React.ComponentType<any>;
}

/**
 * Installation Wizard Component
 * Provides a step-by-step guide for widget installation with live preview
 */
const InstallationWizard: React.FC<InstallationWizardProps> = ({ onComplete, onClose }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [config, setConfig] = useState<WidgetOptions>({
    venueId: '',
    primaryColor: '#6366F1',
    textColor: '#FFFFFF',
    position: 'bottom-right',
    welcomeMessage: 'Hello! How can I help you with your wedding venue inquiry?',
    fontFamily: 'Inter, system-ui, sans-serif',
    fontSize: '16px',
    buttonIcon: 'chat',
    showBranding: true,
    mobileBreakpoint: 768,
    socketUrl: 'https://api.evoque.digital'
  });
  const [selectedPlatform, setSelectedPlatform] = useState<string>('');
  const [embedCode, setEmbedCode] = useState<string>('');
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');

  const steps: WizardStep[] = [
    {
      id: 'platform',
      title: 'Choose Your Platform',
      description: 'Select your website platform for optimized installation',
      component: PlatformSelection
    },
    {
      id: 'configure',
      title: 'Customize Your Widget',
      description: 'Personalize the appearance and behavior',
      component: WidgetConfiguration
    },
    {
      id: 'preview',
      title: 'Preview & Test',
      description: 'See how your widget will look and work',
      component: WidgetPreview
    },
    {
      id: 'install',
      title: 'Get Installation Code',
      description: 'Copy the code and install on your website',
      component: InstallationCode
    }
  ];

  // Generate embed code when config changes
  useEffect(() => {
    if (config.venueId) {
      const code = generateEmbedCode(config);
      setEmbedCode(code);
      
      // Generate QR code URL for mobile installation
      const configParams = new URLSearchParams({
        venueId: config.venueId,
        primaryColor: config.primaryColor,
        position: config.position,
        welcomeMessage: config.welcomeMessage
      });
      const installUrl = `https://widget.evoque.digital/install?${configParams.toString()}`;
      setQrCodeUrl(`https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(installUrl)}`);
    }
  }, [config]);

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      onComplete?.(config);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleConfigChange = (updates: Partial<WidgetOptions>) => {
    setConfig(prev => ({ ...prev, ...updates }));
  };

  const CurrentStepComponent = steps[currentStep].component;

  return (
    <WizardContainer>
      <WizardHeader>
        <CloseButton onClick={onClose}>×</CloseButton>
        <WizardTitle>Widget Installation Wizard</WizardTitle>
        <StepIndicator>
          {steps.map((step, index) => (
            <StepDot 
              key={step.id} 
              active={index === currentStep}
              completed={index < currentStep}
            />
          ))}
        </StepIndicator>
      </WizardHeader>

      <WizardContent>
        <StepInfo>
          <StepTitle>{steps[currentStep].title}</StepTitle>
          <StepDescription>{steps[currentStep].description}</StepDescription>
        </StepInfo>

        <StepContent>
          <CurrentStepComponent
            config={config}
            onConfigChange={handleConfigChange}
            selectedPlatform={selectedPlatform}
            onPlatformChange={setSelectedPlatform}
            embedCode={embedCode}
            qrCodeUrl={qrCodeUrl}
          />
        </StepContent>
      </WizardContent>

      <WizardFooter>
        <Button 
          variant="secondary" 
          onClick={handlePrevious}
          disabled={currentStep === 0}
        >
          Previous
        </Button>
        <Button 
          variant="primary" 
          onClick={handleNext}
        >
          {currentStep === steps.length - 1 ? 'Complete' : 'Next'}
        </Button>
      </WizardFooter>
    </WizardContainer>
  );
};

// Step Components
const PlatformSelection: React.FC<any> = ({ selectedPlatform, onPlatformChange }) => {
  const platforms = [
    { id: 'wordpress', name: 'WordPress', icon: '🔧', description: 'Most popular CMS platform' },
    { id: 'wix', name: 'Wix', icon: '🎨', description: 'Drag-and-drop website builder' },
    { id: 'squarespace', name: 'Squarespace', icon: '📐', description: 'Professional website builder' },
    { id: 'shopify', name: 'Shopify', icon: '🛒', description: 'E-commerce platform' },
    { id: 'webflow', name: 'Webflow', icon: '⚡', description: 'Visual web development' },
    { id: 'custom', name: 'Custom/Other', icon: '💻', description: 'Custom HTML or other platform' }
  ];

  return (
    <PlatformGrid>
      {platforms.map(platform => (
        <PlatformCard
          key={platform.id}
          selected={selectedPlatform === platform.id}
          onClick={() => onPlatformChange(platform.id)}
        >
          <PlatformIcon>{platform.icon}</PlatformIcon>
          <PlatformName>{platform.name}</PlatformName>
          <PlatformDescription>{platform.description}</PlatformDescription>
        </PlatformCard>
      ))}
    </PlatformGrid>
  );
};

const WidgetConfiguration: React.FC<any> = ({ config, onConfigChange }) => {
  return (
    <ConfigurationPanel>
      <ConfigSection>
        <ConfigLabel>Venue ID *</ConfigLabel>
        <ConfigInput
          type="text"
          value={config.venueId}
          onChange={(e) => onConfigChange({ venueId: e.target.value })}
          placeholder="Enter your venue ID"
        />
      </ConfigSection>

      <ConfigSection>
        <ConfigLabel>Primary Color</ConfigLabel>
        <ColorInputGroup>
          <ConfigInput
            type="color"
            value={config.primaryColor}
            onChange={(e) => onConfigChange({ primaryColor: e.target.value })}
          />
          <ConfigInput
            type="text"
            value={config.primaryColor}
            onChange={(e) => onConfigChange({ primaryColor: e.target.value })}
          />
        </ColorInputGroup>
      </ConfigSection>

      <ConfigSection>
        <ConfigLabel>Position</ConfigLabel>
        <ConfigSelect
          value={config.position}
          onChange={(e) => onConfigChange({ position: e.target.value })}
        >
          <option value="bottom-right">Bottom Right</option>
          <option value="bottom-left">Bottom Left</option>
          <option value="top-right">Top Right</option>
          <option value="top-left">Top Left</option>
        </ConfigSelect>
      </ConfigSection>

      <ConfigSection>
        <ConfigLabel>Welcome Message</ConfigLabel>
        <ConfigTextarea
          value={config.welcomeMessage}
          onChange={(e) => onConfigChange({ welcomeMessage: e.target.value })}
          placeholder="Enter your welcome message"
          rows={3}
        />
      </ConfigSection>

      <ConfigSection>
        <ConfigLabel>Button Icon</ConfigLabel>
        <ConfigSelect
          value={config.buttonIcon}
          onChange={(e) => onConfigChange({ buttonIcon: e.target.value })}
        >
          <option value="chat">Chat Bubble</option>
          <option value="message">Message</option>
          <option value="question">Question Mark</option>
          <option value="heart">Heart</option>
        </ConfigSelect>
      </ConfigSection>
    </ConfigurationPanel>
  );
};

const WidgetPreview: React.FC<any> = ({ config }) => {
  return (
    <PreviewContainer>
      <PreviewFrame>
        <MockWebsite>
          <MockHeader>
            <MockLogo>Your Venue Name</MockLogo>
            <MockNav>
              <span>Home</span>
              <span>About</span>
              <span>Gallery</span>
              <span>Contact</span>
            </MockNav>
          </MockHeader>
          <MockContent>
            <MockTitle>Beautiful Wedding Venue</MockTitle>
            <MockText>Create unforgettable memories at our stunning location...</MockText>
          </MockContent>
          
          {/* Live Widget Preview */}
          <Widget options={config} />
        </MockWebsite>
      </PreviewFrame>
      
      <PreviewControls>
        <PreviewButton>Test Widget</PreviewButton>
        <PreviewNote>
          Click the widget button to test the chat interface
        </PreviewNote>
      </PreviewControls>
    </PreviewContainer>
  );
};

const InstallationCode: React.FC<any> = ({ embedCode, qrCodeUrl, selectedPlatform }) => {
  const [copied, setCopied] = useState(false);

  const handleCopy = () => {
    navigator.clipboard.writeText(embedCode);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <InstallationContainer>
      <CodeSection>
        <CodeHeader>
          <CodeTitle>Installation Code</CodeTitle>
          <CopyButton onClick={handleCopy}>
            {copied ? 'Copied!' : 'Copy Code'}
          </CopyButton>
        </CodeHeader>
        <CodeBlock>
          <code>{embedCode}</code>
        </CodeBlock>
      </CodeSection>

      {selectedPlatform && (
        <InstructionsSection>
          <InstructionsTitle>Platform-Specific Instructions</InstructionsTitle>
          <PlatformInstructions platform={selectedPlatform} />
        </InstructionsSection>
      )}

      <QRSection>
        <QRTitle>Mobile Installation</QRTitle>
        <QRCode src={qrCodeUrl} alt="QR Code for mobile installation" />
        <QRDescription>
          Scan this QR code with your phone to access the mobile installation guide
        </QRDescription>
      </QRSection>
    </InstallationContainer>
  );
};

const PlatformInstructions: React.FC<{ platform: string }> = ({ platform }) => {
  const instructions = {
    wordpress: [
      "1. Go to your WordPress admin dashboard",
      "2. Navigate to Appearance → Theme Editor",
      "3. Select your active theme",
      "4. Open the footer.php file",
      "5. Paste the code before the closing </body> tag",
      "6. Click 'Update File'"
    ],
    wix: [
      "1. Open your Wix editor",
      "2. Click 'Add' → 'More' → 'HTML Code'",
      "3. Paste the installation code",
      "4. Position the HTML element anywhere on your page",
      "5. Publish your site"
    ],
    squarespace: [
      "1. Go to Settings → Advanced → Code Injection",
      "2. Paste the code in the Footer section",
      "3. Click 'Save'",
      "4. The widget will appear on all pages"
    ]
  };

  const steps = instructions[platform] || ["Please refer to the general installation guide"];

  return (
    <InstructionsList>
      {steps.map((step, index) => (
        <InstructionStep key={index}>{step}</InstructionStep>
      ))}
    </InstructionsList>
  );
};

// Styled Components
const WizardContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
`;

const WizardContent = styled.div`
  background: white;
  border-radius: 12px;
  width: 90vw;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
`;

const WizardHeader = styled.div`
  padding: 24px;
  border-bottom: 1px solid #e5e7eb;
  position: relative;
`;

const CloseButton = styled.button`
  position: absolute;
  top: 16px;
  right: 16px;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6b7280;
  
  &:hover {
    color: #374151;
  }
`;

const WizardTitle = styled.h2`
  margin: 0 0 16px 0;
  font-size: 24px;
  font-weight: 600;
  color: #111827;
`;

const StepIndicator = styled.div`
  display: flex;
  gap: 8px;
`;

const StepDot = styled.div<{ active: boolean; completed: boolean }>`
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: ${props => 
    props.completed ? '#10b981' : 
    props.active ? '#6366f1' : '#d1d5db'
  };
  transition: background 0.2s;
`;

const StepInfo = styled.div`
  padding: 24px 24px 0 24px;
`;

const StepTitle = styled.h3`
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #111827;
`;

const StepDescription = styled.p`
  margin: 0;
  color: #6b7280;
  font-size: 16px;
`;

const StepContent = styled.div`
  padding: 24px;
  min-height: 400px;
`;

const WizardFooter = styled.div`
  padding: 24px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
`;

const Button = styled.button<{ variant: 'primary' | 'secondary'; disabled?: boolean }>`
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  
  ${props => props.variant === 'primary' ? `
    background: #6366f1;
    color: white;
    border: none;
    
    &:hover {
      background: #5856eb;
    }
  ` : `
    background: white;
    color: #374151;
    border: 1px solid #d1d5db;
    
    &:hover {
      background: #f9fafb;
    }
  `}
  
  ${props => props.disabled && `
    opacity: 0.5;
    cursor: not-allowed;
    
    &:hover {
      background: ${props.variant === 'primary' ? '#6366f1' : 'white'};
    }
  `}
`;

const PlatformGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
`;

const PlatformCard = styled.div<{ selected: boolean }>`
  padding: 20px;
  border: 2px solid ${props => props.selected ? '#6366f1' : '#e5e7eb'};
  border-radius: 12px;
  cursor: pointer;
  text-align: center;
  transition: all 0.2s;
  background: ${props => props.selected ? '#f0f9ff' : 'white'};
  
  &:hover {
    border-color: #6366f1;
    background: #f0f9ff;
  }
`;

const PlatformIcon = styled.div`
  font-size: 32px;
  margin-bottom: 12px;
`;

const PlatformName = styled.h4`
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
`;

const PlatformDescription = styled.p`
  margin: 0;
  font-size: 14px;
  color: #6b7280;
`;

const ConfigurationPanel = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const ConfigSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const ConfigLabel = styled.label`
  font-weight: 500;
  color: #374151;
  font-size: 14px;
`;

const ConfigInput = styled.input`
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  
  &:focus {
    outline: none;
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  }
`;

const ColorInputGroup = styled.div`
  display: flex;
  gap: 12px;
  
  input[type="color"] {
    width: 60px;
    height: 44px;
    padding: 4px;
  }
  
  input[type="text"] {
    flex: 1;
  }
`;

const ConfigSelect = styled.select`
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  
  &:focus {
    outline: none;
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  }
`;

const ConfigTextarea = styled.textarea`
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  resize: vertical;
  font-family: inherit;
  
  &:focus {
    outline: none;
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  }
`;

const PreviewContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const PreviewFrame = styled.div`
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  height: 300px;
  position: relative;
`;

const MockWebsite = styled.div`
  height: 100%;
  background: #f9fafb;
  position: relative;
`;

const MockHeader = styled.div`
  background: white;
  padding: 16px 24px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const MockLogo = styled.div`
  font-weight: 600;
  color: #111827;
`;

const MockNav = styled.div`
  display: flex;
  gap: 24px;
  
  span {
    color: #6b7280;
    font-size: 14px;
  }
`;

const MockContent = styled.div`
  padding: 40px 24px;
  text-align: center;
`;

const MockTitle = styled.h1`
  margin: 0 0 16px 0;
  font-size: 32px;
  color: #111827;
`;

const MockText = styled.p`
  margin: 0;
  color: #6b7280;
  font-size: 16px;
`;

const PreviewControls = styled.div`
  text-align: center;
`;

const PreviewButton = styled.button`
  padding: 12px 24px;
  background: #6366f1;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  margin-bottom: 8px;
  
  &:hover {
    background: #5856eb;
  }
`;

const PreviewNote = styled.p`
  margin: 0;
  font-size: 14px;
  color: #6b7280;
`;

const InstallationContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 24px;
`;

const CodeSection = styled.div``;

const CodeHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
`;

const CodeTitle = styled.h4`
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
`;

const CopyButton = styled.button`
  padding: 8px 16px;
  background: #10b981;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  
  &:hover {
    background: #059669;
  }
`;

const CodeBlock = styled.pre`
  background: #f3f4f6;
  padding: 16px;
  border-radius: 8px;
  overflow-x: auto;
  font-size: 14px;
  line-height: 1.5;
  
  code {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  }
`;

const InstructionsSection = styled.div``;

const InstructionsTitle = styled.h4`
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
`;

const InstructionsList = styled.ol`
  margin: 0;
  padding-left: 20px;
`;

const InstructionStep = styled.li`
  margin-bottom: 8px;
  color: #374151;
  line-height: 1.5;
`;

const QRSection = styled.div`
  text-align: center;
`;

const QRTitle = styled.h4`
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
`;

const QRCode = styled.img`
  width: 150px;
  height: 150px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 8px;
`;

const QRDescription = styled.p`
  margin: 0;
  font-size: 14px;
  color: #6b7280;
`;

export default InstallationWizard;
