import React from 'react';
import {
  ChatBubbleLeftRightIcon,
  UserGroupIcon,
  TrophyIcon,
  ClockIcon,
  FireIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';

interface LiveActivity {
  id: string;
  type: 'inquiry' | 'lead_update' | 'booking' | 'response';
  title: string;
  description: string;
  timestamp: Date;
  priority: 'low' | 'medium' | 'high';
  data?: any;
}

interface LiveActivityFeedProps {
  activities: LiveActivity[];
  maxItems?: number;
}

/**
 * Live activity feed showing real-time events
 */
const LiveActivityFeed: React.FC<LiveActivityFeedProps> = ({ 
  activities, 
  maxItems = 20 
}) => {
  const getActivityIcon = (type: string, priority: string) => {
    const iconClass = "h-5 w-5";
    
    switch (type) {
      case 'inquiry':
        return priority === 'high' ? (
          <FireIcon className={`${iconClass} text-red-500`} />
        ) : (
          <ChatBubbleLeftRightIcon className={`${iconClass} text-blue-500`} />
        );
      case 'lead_update':
        return <UserGroupIcon className={`${iconClass} text-purple-500`} />;
      case 'booking':
        return <TrophyIcon className={`${iconClass} text-green-500`} />;
      case 'response':
        return <CheckCircleIcon className={`${iconClass} text-emerald-500`} />;
      default:
        return <ClockIcon className={`${iconClass} text-gray-500`} />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'border-l-red-500 bg-red-50';
      case 'medium':
        return 'border-l-amber-500 bg-amber-50';
      case 'low':
        return 'border-l-blue-500 bg-blue-50';
      default:
        return 'border-l-gray-500 bg-gray-50';
    }
  };

  const formatTimeAgo = (timestamp: Date) => {
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - timestamp.getTime()) / 1000);
    
    if (diffInSeconds < 60) {
      return `${diffInSeconds}s ago`;
    } else if (diffInSeconds < 3600) {
      return `${Math.floor(diffInSeconds / 60)}m ago`;
    } else if (diffInSeconds < 86400) {
      return `${Math.floor(diffInSeconds / 3600)}h ago`;
    } else {
      return `${Math.floor(diffInSeconds / 86400)}d ago`;
    }
  };

  const displayActivities = activities.slice(0, maxItems);

  if (displayActivities.length === 0) {
    return (
      <div className="p-6 text-center">
        <ClockIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-500">No recent activity</p>
        <p className="text-sm text-gray-400 mt-1">
          Activity will appear here in real-time
        </p>
      </div>
    );
  }

  return (
    <div className="max-h-96 overflow-y-auto">
      <div className="divide-y divide-gray-200">
        {displayActivities.map((activity, index) => (
          <div
            key={activity.id}
            className={`p-4 border-l-4 ${getPriorityColor(activity.priority)} ${
              index === 0 ? 'animate-pulse' : ''
            }`}
          >
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 mt-0.5">
                {getActivityIcon(activity.type, activity.priority)}
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {activity.title}
                    {activity.priority === 'high' && (
                      <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        HOT
                      </span>
                    )}
                  </p>
                  <p className="text-xs text-gray-500 flex-shrink-0 ml-2">
                    {formatTimeAgo(activity.timestamp)}
                  </p>
                </div>
                
                <p className="text-sm text-gray-600 mt-1">
                  {activity.description}
                </p>
                
                {/* Additional data display based on activity type */}
                {activity.type === 'inquiry' && activity.data?.leadScore && (
                  <div className="mt-2">
                    <div className="flex items-center space-x-2">
                      <span className="text-xs text-gray-500">Lead Score:</span>
                      <div className="flex-1 bg-gray-200 rounded-full h-2 max-w-20">
                        <div
                          className={`h-2 rounded-full ${
                            activity.data.leadScore > 80
                              ? 'bg-red-500'
                              : activity.data.leadScore > 60
                              ? 'bg-amber-500'
                              : 'bg-blue-500'
                          }`}
                          style={{ width: `${activity.data.leadScore}%` }}
                        ></div>
                      </div>
                      <span className="text-xs font-medium text-gray-700">
                        {activity.data.leadScore}
                      </span>
                    </div>
                  </div>
                )}
                
                {activity.type === 'booking' && activity.data?.value && (
                  <div className="mt-2">
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      ${activity.data.value.toLocaleString()} revenue
                    </span>
                  </div>
                )}
                
                {activity.type === 'response' && activity.data?.responseTime && (
                  <div className="mt-2">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      activity.data.responseTime < 15
                        ? 'bg-green-100 text-green-800'
                        : activity.data.responseTime < 60
                        ? 'bg-amber-100 text-amber-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {activity.data.responseTime < 60
                        ? `${activity.data.responseTime}m response`
                        : `${Math.round(activity.data.responseTime / 60)}h response`
                      }
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
      
      {activities.length > maxItems && (
        <div className="p-4 text-center border-t border-gray-200">
          <p className="text-sm text-gray-500">
            Showing {maxItems} of {activities.length} activities
          </p>
        </div>
      )}
    </div>
  );
};

export default LiveActivityFeed;
