# Evoque Platform - Production Deployment Guide

## 🚀 Production Readiness Status: READY FOR DEPLOYMENT ✅

All features have been implemented and tested. The platform is ready for production launch.

---

## 📋 Pre-Deployment Checklist

### ✅ Implementation Status
- ✅ **Backend API** - All GraphQL resolvers and REST endpoints implemented
- ✅ **Real-Time Features** - WebSocket service with push notifications
- ✅ **Widget System** - Installation wizard, QR codes, platform integration
- ✅ **Dashboard** - Live analytics with real-time updates
- ✅ **Database Schema** - All tables, relationships, and migrations ready
- ✅ **Security** - Authentication, authorization, input validation
- ✅ **Error Handling** - Comprehensive error boundaries and fallbacks
- ✅ **Performance** - Optimized for production load

---

## 🔧 Environment Configuration

### Required Environment Variables

Create a `.env` file in the `evoque-api` directory with the following variables:

```bash
# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DATABASE_URL="****************************************/evoque_production"
DIRECT_URL="****************************************/evoque_production"

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
NODE_ENV="production"
PORT="4000"
JWT_SECRET="your-super-secure-jwt-secret-key-min-32-chars"
JWT_EXPIRES_IN="7d"

# =============================================================================
# CORS AND SECURITY
# =============================================================================
CORS_ORIGIN="https://dashboard.evoque.digital,https://evoque.digital"
ALLOWED_ORIGINS="https://dashboard.evoque.digital,https://evoque.digital"
DASHBOARD_URL="https://dashboard.evoque.digital"

# =============================================================================
# GRAPHQL CONFIGURATION
# =============================================================================
GRAPHQL_WS_URL="wss://api.evoque.digital/graphql"
NEXT_PUBLIC_GRAPHQL_URL="https://api.evoque.digital/graphql"
NEXT_PUBLIC_GRAPHQL_WS_URL="wss://api.evoque.digital/graphql"

# =============================================================================
# FIREBASE CLOUD MESSAGING (Push Notifications)
# =============================================================================
FIREBASE_SERVER_KEY="your-firebase-server-key"
# Get from: Firebase Console > Project Settings > Cloud Messaging > Server Key

# =============================================================================
# TWILIO (SMS Notifications)
# =============================================================================
TWILIO_ACCOUNT_SID="your-twilio-account-sid"
TWILIO_AUTH_TOKEN="your-twilio-auth-token"
TWILIO_PHONE_NUMBER="+**********"
# Get from: https://console.twilio.com/

# =============================================================================
# RESEND (Email Notifications)
# =============================================================================
RESEND_API_KEY="re_your-resend-api-key"
# Get from: https://resend.com/api-keys

# =============================================================================
# CDN AND STATIC ASSETS
# =============================================================================
CDN_URL="https://cdn.evoque.digital"
WIDGET_CDN_URL="https://cdn.evoque.digital/widget/v2"

# =============================================================================
# MONITORING AND LOGGING
# =============================================================================
LOG_LEVEL="info"
SENTRY_DSN="your-sentry-dsn-for-error-tracking"
```

### Dashboard Environment Variables

Create a `.env.local` file in the `evoque-dashboard` directory:

```bash
# =============================================================================
# API CONFIGURATION
# =============================================================================
NEXT_PUBLIC_API_URL="https://api.evoque.digital"
NEXT_PUBLIC_GRAPHQL_URL="https://api.evoque.digital/graphql"
NEXT_PUBLIC_GRAPHQL_WS_URL="wss://api.evoque.digital/graphql"

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
NEXTAUTH_URL="https://dashboard.evoque.digital"
NEXTAUTH_SECRET="your-nextauth-secret-key"
NODE_ENV="production"

# =============================================================================
# WIDGET CONFIGURATION
# =============================================================================
NEXT_PUBLIC_WIDGET_CDN_URL="https://cdn.evoque.digital/widget/v2"
NEXT_PUBLIC_WIDGET_LOADER_URL="https://cdn.evoque.digital/widget/v2/loader.js"
```

---

## 🗄️ Database Setup

### 1. Create Production Database

```sql
-- Create database
CREATE DATABASE evoque_production;

-- Create user (if needed)
CREATE USER evoque_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE evoque_production TO evoque_user;
```

### 2. Run Database Migrations

```bash
cd evoque-api

# Generate Prisma client
npm run prisma:generate

# Run migrations
npm run prisma:migrate

# Seed initial data
npm run seed
```

### 3. Verify Database Setup

```bash
# Check database connection
npm run health:check

# Validate schema
npm run validate
```

---

## 🚀 Deployment Steps

### Step 1: Prepare Infrastructure

```bash
# 1. Set up production server (Ubuntu 20.04+ recommended)
# 2. Install Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 3. Install PM2 for process management
sudo npm install -g pm2

# 4. Install PostgreSQL
sudo apt-get install postgresql postgresql-contrib

# 5. Set up SSL certificates (Let's Encrypt recommended)
sudo apt-get install certbot
```

### Step 2: Deploy Backend API

```bash
# Clone repository
git clone https://github.com/your-org/evoque-platform.git
cd evoque-platform/evoque-api

# Install dependencies
npm ci --only=production

# Set up environment variables
cp .env.example .env
# Edit .env with production values

# Build application
npm run build

# Run database migrations
npm run prisma:migrate

# Start with PM2
pm2 start ecosystem.config.js --env production
pm2 save
pm2 startup
```

### Step 3: Deploy Dashboard

```bash
cd ../evoque-dashboard

# Install dependencies
npm ci --only=production

# Set up environment variables
cp .env.example .env.local
# Edit .env.local with production values

# Build application
npm run build

# Start with PM2
pm2 start npm --name "evoque-dashboard" -- start
```

### Step 4: Deploy Widget to CDN

```bash
cd ../evoque-api

# Run CDN deployment script
npm run cdn:deploy

# Upload generated files to your CDN
# Files will be in ./cdn-dist/widget/v2/
```

### Step 5: Configure Reverse Proxy (Nginx)

```nginx
# /etc/nginx/sites-available/evoque
server {
    listen 80;
    server_name api.evoque.digital;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name api.evoque.digital;

    ssl_certificate /etc/letsencrypt/live/api.evoque.digital/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/api.evoque.digital/privkey.pem;

    location / {
        proxy_pass http://localhost:4000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}

server {
    listen 80;
    server_name dashboard.evoque.digital;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name dashboard.evoque.digital;

    ssl_certificate /etc/letsencrypt/live/dashboard.evoque.digital/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/dashboard.evoque.digital/privkey.pem;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

---

## 🔍 Post-Deployment Validation

### Health Checks

```bash
# Check API health
curl https://api.evoque.digital/health

# Check GraphQL endpoint
curl -X POST https://api.evoque.digital/graphql \
  -H "Content-Type: application/json" \
  -d '{"query": "{ __typename }"}'

# Check WebSocket connections
curl https://api.evoque.digital/api/stats/connections

# Check dashboard
curl https://dashboard.evoque.digital
```

### Functional Testing

```bash
# Run validation scripts
cd evoque-api
npm run validate
npm run validate:platform
npm run validate:analytics

# Test widget installation
# Visit: https://cdn.evoque.digital/widget/v2/examples/basic.html
```

### Performance Testing

```bash
# Test widget load time
curl -w "@curl-format.txt" -o /dev/null -s https://cdn.evoque.digital/widget/v2/loader.js

# Test API response time
curl -w "@curl-format.txt" -o /dev/null -s https://api.evoque.digital/health
```

---

## 📊 Monitoring Setup

### PM2 Monitoring

```bash
# Monitor processes
pm2 monit

# View logs
pm2 logs

# Restart services
pm2 restart all
```

### Database Monitoring

```sql
-- Monitor active connections
SELECT count(*) FROM pg_stat_activity;

-- Monitor query performance
SELECT query, mean_time, calls FROM pg_stat_statements ORDER BY mean_time DESC LIMIT 10;
```

### Application Monitoring

```bash
# Check disk usage
df -h

# Check memory usage
free -h

# Check CPU usage
top

# Check network connections
netstat -tulpn | grep :4000
```

---

## 🚨 Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check DATABASE_URL format
   - Verify PostgreSQL is running
   - Check firewall settings

2. **WebSocket Connection Failed**
   - Verify Nginx WebSocket proxy configuration
   - Check CORS settings
   - Ensure SSL certificates are valid

3. **Widget Not Loading**
   - Check CDN deployment
   - Verify CORS headers
   - Test widget URL directly

4. **Push Notifications Not Working**
   - Verify Firebase server key
   - Check notification permissions
   - Test FCM endpoint directly

### Log Locations

```bash
# PM2 logs
~/.pm2/logs/

# Nginx logs
/var/log/nginx/

# System logs
/var/log/syslog
```

---

## ✅ Production Launch Checklist

- [ ] **Environment Variables** - All required variables configured
- [ ] **Database** - Migrations run, data seeded
- [ ] **API Deployment** - Backend services running
- [ ] **Dashboard Deployment** - Frontend application running
- [ ] **CDN Deployment** - Widget assets deployed
- [ ] **SSL Certificates** - HTTPS configured for all domains
- [ ] **Health Checks** - All endpoints responding correctly
- [ ] **Performance Testing** - Load times under 2 seconds
- [ ] **Security Audit** - Authentication and authorization working
- [ ] **Monitoring** - Error tracking and performance monitoring active
- [ ] **Backup Strategy** - Database backups configured
- [ ] **Documentation** - Deployment and operational procedures documented

**Status: READY FOR PRODUCTION LAUNCH** 🚀
