import React, { useState, useEffect } from 'react';
import { useQuery, useSubscription, gql } from '@apollo/client';
import { useAuth } from '@/context/AuthContext';
import {
  ChartBarIcon,
  ChatBubbleLeftRightIcon,
  ClockIcon,
  CurrencyDollarIcon,
  UserGroupIcon,
  TrophyIcon,
  BellIcon,
  EyeIcon,
} from '@heroicons/react/24/outline';
import StatsCard from './StatsCard';
import RecentInquiries from './RecentInquiries';
import LiveActivityFeed from './LiveActivityFeed';
import RealTimeChart from './RealTimeChart';

// GraphQL Subscriptions
const INQUIRY_RECEIVED_SUBSCRIPTION = gql`
  subscription InquiryReceived($venueId: ID!) {
    inquiryReceived(venueId: $venueId) {
      id
      contactName
      contactEmail
      eventDate
      status
      source
      createdAt
      leadScore
    }
  }
`;

const INQUIRY_UPDATED_SUBSCRIPTION = gql`
  subscription InquiryUpdated($venueId: ID!) {
    inquiryUpdated(venueId: $venueId) {
      id
      status
      responseTime
      updatedAt
    }
  }
`;

const LEAD_UPDATED_SUBSCRIPTION = gql`
  subscription LeadUpdated($venueId: ID!) {
    leadUpdated(venueId: $venueId) {
      id
      status
      value
      probability
      updatedAt
    }
  }
`;

// Real-time dashboard query
const REAL_TIME_DASHBOARD_QUERY = gql`
  query RealTimeDashboard($venueId: ID!) {
    analyticsOverview(venueId: $venueId, period: "today") {
      inquiryStats {
        total
        new
        responded
        avgResponseTime
      }
      leadStats {
        total
        new
        active
        won
        lost
        avgValue
      }
      conversionStats {
        inquiryToLeadRate
        leadToBookingRate
        overallConversionRate
      }
    }
    inquiries(venueId: $venueId, filter: { status: "new" }, pagination: { first: 10 }) {
      edges {
        node {
          id
          contactName
          contactEmail
          eventDate
          status
          source
          createdAt
          leadScore
        }
      }
    }
    liveMetrics(venueId: $venueId) {
      activeVisitors
      onlineUsers
      chatSessions
      responseRate
      avgSessionDuration
    }
  }
`;

interface RealTimeDashboardProps {
  venueId: string;
}

interface LiveActivity {
  id: string;
  type: 'inquiry' | 'lead_update' | 'booking' | 'response';
  title: string;
  description: string;
  timestamp: Date;
  priority: 'low' | 'medium' | 'high';
  data?: any;
}

/**
 * Real-time dashboard with live updates via GraphQL subscriptions
 */
const RealTimeDashboard: React.FC<RealTimeDashboardProps> = ({ venueId }) => {
  const { user } = useAuth();
  const [liveActivities, setLiveActivities] = useState<LiveActivity[]>([]);
  const [notifications, setNotifications] = useState<string[]>([]);
  const [isLive, setIsLive] = useState(true);

  // Main dashboard data
  const { data, loading, error, refetch } = useQuery(REAL_TIME_DASHBOARD_QUERY, {
    variables: { venueId },
    pollInterval: isLive ? 30000 : 0, // Poll every 30 seconds when live
    errorPolicy: 'all'
  });

  // Real-time subscriptions
  const { data: newInquiry } = useSubscription(INQUIRY_RECEIVED_SUBSCRIPTION, {
    variables: { venueId },
    skip: !isLive
  });

  const { data: updatedInquiry } = useSubscription(INQUIRY_UPDATED_SUBSCRIPTION, {
    variables: { venueId },
    skip: !isLive
  });

  const { data: updatedLead } = useSubscription(LEAD_UPDATED_SUBSCRIPTION, {
    variables: { venueId },
    skip: !isLive
  });

  // Handle new inquiry subscription
  useEffect(() => {
    if (newInquiry?.inquiryReceived) {
      const inquiry = newInquiry.inquiryReceived;
      
      // Add to live activity feed
      const activity: LiveActivity = {
        id: `inquiry-${inquiry.id}`,
        type: 'inquiry',
        title: 'New Inquiry Received',
        description: `${inquiry.contactName} inquired about ${new Date(inquiry.eventDate).toLocaleDateString()}`,
        timestamp: new Date(),
        priority: inquiry.leadScore > 80 ? 'high' : inquiry.leadScore > 60 ? 'medium' : 'low',
        data: inquiry
      };

      setLiveActivities(prev => [activity, ...prev.slice(0, 19)]); // Keep last 20 activities
      
      // Add notification
      const notification = `New ${inquiry.leadScore > 80 ? 'HOT' : ''} inquiry from ${inquiry.contactName}`;
      setNotifications(prev => [notification, ...prev.slice(0, 4)]); // Keep last 5 notifications
      
      // Show browser notification if permission granted
      if (Notification.permission === 'granted') {
        new Notification('New Inquiry', {
          body: notification,
          icon: '/favicon.ico'
        });
      }
      
      // Refetch data to update stats
      refetch();
    }
  }, [newInquiry, refetch]);

  // Handle inquiry updates
  useEffect(() => {
    if (updatedInquiry?.inquiryUpdated) {
      const inquiry = updatedInquiry.inquiryUpdated;
      
      const activity: LiveActivity = {
        id: `inquiry-update-${inquiry.id}`,
        type: 'response',
        title: 'Inquiry Responded',
        description: `Response time: ${inquiry.responseTime} minutes`,
        timestamp: new Date(),
        priority: inquiry.responseTime < 15 ? 'high' : 'medium',
        data: inquiry
      };

      setLiveActivities(prev => [activity, ...prev.slice(0, 19)]);
      refetch();
    }
  }, [updatedInquiry, refetch]);

  // Handle lead updates
  useEffect(() => {
    if (updatedLead?.leadUpdated) {
      const lead = updatedLead.leadUpdated;
      
      const activity: LiveActivity = {
        id: `lead-update-${lead.id}`,
        type: 'lead_update',
        title: 'Lead Updated',
        description: `Status: ${lead.status}, Value: $${lead.value?.toLocaleString()}`,
        timestamp: new Date(),
        priority: lead.status === 'won' ? 'high' : 'medium',
        data: lead
      };

      setLiveActivities(prev => [activity, ...prev.slice(0, 19)]);
      
      if (lead.status === 'won') {
        const notification = `🎉 Booking confirmed! $${lead.value?.toLocaleString()} revenue`;
        setNotifications(prev => [notification, ...prev.slice(0, 4)]);
        
        if (Notification.permission === 'granted') {
          new Notification('Booking Confirmed!', {
            body: notification,
            icon: '/favicon.ico'
          });
        }
      }
      
      refetch();
    }
  }, [updatedLead, refetch]);

  // Request notification permission on mount
  useEffect(() => {
    if (Notification.permission === 'default') {
      Notification.requestPermission();
    }
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <div className="flex">
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">
              Error loading dashboard
            </h3>
            <div className="mt-2 text-sm text-red-700">
              {error.message}
            </div>
          </div>
        </div>
      </div>
    );
  }

  const overview = data?.analyticsOverview;
  const recentInquiries = data?.inquiries?.edges?.map(edge => edge.node) || [];
  const liveMetrics = data?.liveMetrics;

  return (
    <div className="space-y-6">
      {/* Live Status Header */}
      <div className="bg-white rounded-lg shadow p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className={`w-3 h-3 rounded-full ${isLive ? 'bg-green-500 animate-pulse' : 'bg-gray-400'}`}></div>
            <h2 className="text-lg font-medium text-gray-900">
              {isLive ? 'Live Dashboard' : 'Dashboard (Paused)'}
            </h2>
            <button
              onClick={() => setIsLive(!isLive)}
              className={`px-3 py-1 rounded-full text-sm font-medium ${
                isLive 
                  ? 'bg-green-100 text-green-800 hover:bg-green-200' 
                  : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
              }`}
            >
              {isLive ? 'Pause' : 'Resume'}
            </button>
          </div>
          
          {/* Live Notifications */}
          {notifications.length > 0 && (
            <div className="flex items-center space-x-2">
              <BellIcon className="h-5 w-5 text-amber-500" />
              <div className="text-sm text-gray-600">
                {notifications[0]}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Live Metrics */}
      {liveMetrics && (
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-5">
          <StatsCard
            title="Active Visitors"
            value={liveMetrics.activeVisitors || 0}
            icon={EyeIcon}
            iconColor="bg-blue-500"
            isLive={true}
          />
          <StatsCard
            title="Online Users"
            value={liveMetrics.onlineUsers || 0}
            icon={UserGroupIcon}
            iconColor="bg-green-500"
            isLive={true}
          />
          <StatsCard
            title="Chat Sessions"
            value={liveMetrics.chatSessions || 0}
            icon={ChatBubbleLeftRightIcon}
            iconColor="bg-purple-500"
            isLive={true}
          />
          <StatsCard
            title="Response Rate"
            value={`${(liveMetrics.responseRate * 100 || 0).toFixed(1)}%`}
            icon={ChartBarIcon}
            iconColor="bg-indigo-500"
            isLive={true}
          />
          <StatsCard
            title="Avg Session"
            value={`${Math.round(liveMetrics.avgSessionDuration / 60) || 0}m`}
            icon={ClockIcon}
            iconColor="bg-amber-500"
            isLive={true}
          />
        </div>
      )}

      {/* Main Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <StatsCard
          title="Today's Inquiries"
          value={overview?.inquiryStats.new || 0}
          icon={ChatBubbleLeftRightIcon}
          change={10}
          trend="up"
          iconColor="bg-primary-500"
        />
        <StatsCard
          title="Conversion Rate"
          value={`${(overview?.conversionStats.inquiryToLeadRate * 100 || 0).toFixed(1)}%`}
          icon={ChartBarIcon}
          change={2.5}
          trend="up"
          iconColor="bg-success-500"
        />
        <StatsCard
          title="Avg Response Time"
          value={`${overview?.inquiryStats.avgResponseTime || 0}m`}
          icon={ClockIcon}
          change={-15}
          trend="down"
          iconColor="bg-warning-500"
          trendPositive={true}
        />
        <StatsCard
          title="Active Leads"
          value={overview?.leadStats.active || 0}
          icon={TrophyIcon}
          iconColor="bg-secondary-500"
        />
      </div>

      {/* Real-time Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Live Activity Feed */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-5 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900 flex items-center">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-2"></div>
                Live Activity
              </h3>
            </div>
            <LiveActivityFeed activities={liveActivities} />
          </div>
        </div>

        {/* Recent Inquiries with Real-time Updates */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-5 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Recent Inquiries</h3>
            </div>
            <RecentInquiries inquiries={recentInquiries} showLiveIndicator={true} />
          </div>
        </div>

        {/* Real-time Chart */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Live Metrics</h3>
            <RealTimeChart 
              data={liveActivities}
              isLive={isLive}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default RealTimeDashboard;
