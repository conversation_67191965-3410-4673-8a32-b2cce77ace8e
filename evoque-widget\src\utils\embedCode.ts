import { WidgetOptions } from '../types';

/**
 * Embed code generation utilities for the Evoque widget
 */

interface EmbedCodeOptions {
  format?: 'script-tag' | 'npm' | 'cdn' | 'iframe';
  minified?: boolean;
  async?: boolean;
  defer?: boolean;
  integrity?: string;
  crossOrigin?: 'anonymous' | 'use-credentials';
  fallbackMode?: boolean;
  debug?: boolean;
}

interface PlatformSpecificOptions {
  platform?: 'wordpress' | 'wix' | 'squarespace' | 'shopify' | 'webflow' | 'custom';
  includeInstructions?: boolean;
  wrapInComments?: boolean;
}

/**
 * Generates the embed code for the widget based on the provided options
 *
 * @param options Widget configuration options
 * @param cdnUrl URL for the CDN-hosted widget (default: https://cdn.evoque.digital/widget/v2/loader.js)
 * @returns HTML embed code as a string
 */
export const generateEmbedCode = (
  options: WidgetOptions,
  cdnUrl: string = 'https://cdn.evoque.digital/widget/v2/loader.js'
): string => {
  // Create data attributes for the universal loader
  const dataAttributes: string[] = [];

  // Always include venueId
  dataAttributes.push(`data-venue-id="${options.venueId}"`);

  // Only include other options if they differ from defaults
  if (options.primaryColor !== '#6366F1') {
    dataAttributes.push(`data-primary-color="${options.primaryColor}"`);
  }
  
  if (options.textColor !== '#FFFFFF') {
    dataAttributes.push(`data-text-color="${options.textColor}"`);
  }

  if (options.position !== 'bottom-right') {
    dataAttributes.push(`data-position="${options.position}"`);
  }

  if (options.welcomeMessage !== 'Hello! How can I help you with your wedding venue inquiry?') {
    dataAttributes.push(`data-welcome-message="${options.welcomeMessage}"`);
  }

  if (options.fontFamily !== 'Inter, system-ui, sans-serif') {
    dataAttributes.push(`data-font-family="${options.fontFamily}"`);
  }

  if (options.fontSize !== '16px') {
    dataAttributes.push(`data-font-size="${options.fontSize}"`);
  }

  if (options.buttonIcon !== 'chat') {
    dataAttributes.push(`data-button-icon="${options.buttonIcon}"`);
  }

  if (options.showBranding !== true) {
    dataAttributes.push(`data-show-branding="${options.showBranding}"`);
  }

  if (options.mobileBreakpoint !== 768) {
    dataAttributes.push(`data-mobile-breakpoint="${options.mobileBreakpoint}"`);
  }

  if (options.socketUrl !== 'https://api.evoque.digital') {
    dataAttributes.push(`data-socket-url="${options.socketUrl}"`);
  }

  // Generate the universal loader script tag
  const attributesString = dataAttributes.join('\n        ');

  return `<script src="${cdnUrl}"
        ${attributesString}
        async defer>
</script>`;
};

/**
 * Generate enhanced embed code with additional options
 */
export const generateEnhancedEmbedCode = (
  options: WidgetOptions,
  embedOptions: EmbedCodeOptions = {},
  platformOptions: PlatformSpecificOptions = {}
): string => {
  const {
    format = 'script-tag',
    minified = true,
    async = true,
    defer = true,
    integrity,
    crossOrigin = 'anonymous',
    fallbackMode = false,
    debug = false
  } = embedOptions;

  const {
    platform = 'custom',
    includeInstructions = false,
    wrapInComments = false
  } = platformOptions;

  let code = '';

  // Add platform-specific comments
  if (wrapInComments) {
    code += `<!-- Evoque Widget for ${platform.charAt(0).toUpperCase() + platform.slice(1)} -->\n`;
  }

  // Add instructions if requested
  if (includeInstructions) {
    code += generateInstallationInstructions(platform);
  }

  switch (format) {
    case 'script-tag':
      code += generateScriptTagCode(options, embedOptions);
      break;
    case 'iframe':
      code += generateIframeCode(options);
      break;
    case 'npm':
      code += generateNpmCodeSnippet(options);
      break;
    case 'cdn':
      code += generateCDNCode(options, embedOptions);
      break;
    default:
      code += generateEmbedCode(options);
  }

  if (wrapInComments) {
    code += '\n<!-- End Evoque Widget -->';
  }

  return code;
};

/**
 * Generate script tag with enhanced options
 */
const generateScriptTagCode = (options: WidgetOptions, embedOptions: EmbedCodeOptions): string => {
  const cdnUrl = 'https://cdn.evoque.digital/widget/v2/loader.js';
  const dataAttributes: string[] = [];

  // Add all configuration as data attributes
  dataAttributes.push(`data-venue-id="${options.venueId}"`);

  if (options.primaryColor !== '#6366F1') {
    dataAttributes.push(`data-primary-color="${options.primaryColor}"`);
  }

  if (options.textColor !== '#FFFFFF') {
    dataAttributes.push(`data-text-color="${options.textColor}"`);
  }

  if (options.position !== 'bottom-right') {
    dataAttributes.push(`data-position="${options.position}"`);
  }

  if (options.welcomeMessage !== 'Hello! How can I help you with your wedding venue inquiry?') {
    dataAttributes.push(`data-welcome-message="${options.welcomeMessage}"`);
  }

  // Add debug mode if enabled
  if (embedOptions.debug) {
    dataAttributes.push('data-debug="true"');
  }

  // Add fallback mode if enabled
  if (embedOptions.fallbackMode) {
    dataAttributes.push('data-fallback-mode="true"');
  }

  const attributes = dataAttributes.join('\n        ');
  const scriptAttributes = [];

  if (embedOptions.async) scriptAttributes.push('async');
  if (embedOptions.defer) scriptAttributes.push('defer');
  if (embedOptions.integrity) scriptAttributes.push(`integrity="${embedOptions.integrity}"`);
  if (embedOptions.crossOrigin) scriptAttributes.push(`crossorigin="${embedOptions.crossOrigin}"`);

  const scriptAttrs = scriptAttributes.length > 0 ? ' ' + scriptAttributes.join(' ') : '';

  return `<script src="${cdnUrl}"
        ${attributes}${scriptAttrs}>
</script>`;
};

/**
 * Generate iframe embed code for restrictive environments
 */
const generateIframeCode = (options: WidgetOptions): string => {
  const params = new URLSearchParams({
    venueId: options.venueId,
    primaryColor: options.primaryColor,
    textColor: options.textColor,
    position: options.position,
    welcomeMessage: options.welcomeMessage,
    fontFamily: options.fontFamily,
    fontSize: options.fontSize,
    buttonIcon: options.buttonIcon,
    showBranding: options.showBranding.toString(),
    mobileBreakpoint: options.mobileBreakpoint.toString()
  });

  return `<iframe src="https://cdn.evoque.digital/widget/v2/iframe.html?${params.toString()}"
        width="60"
        height="60"
        frameborder="0"
        style="position: fixed; bottom: 20px; right: 20px; z-index: 999999; border-radius: 50%; box-shadow: 0 4px 12px rgba(0,0,0,0.15);"
        title="Evoque Wedding Venue Chat">
</iframe>`;
};

/**
 * Generate CDN-optimized code
 */
const generateCDNCode = (options: WidgetOptions, embedOptions: EmbedCodeOptions): string => {
  const version = embedOptions.minified ? 'v2' : 'v2/dev';
  const cdnUrl = `https://cdn.evoque.digital/widget/${version}/loader.js`;

  return generateScriptTagCode(options, { ...embedOptions, async: true, defer: true });
};

/**
 * Generate installation instructions for different platforms
 */
const generateInstallationInstructions = (platform: string): string => {
  const instructions = {
    wordpress: `<!-- WordPress Installation Instructions:
1. Go to Appearance → Theme Editor
2. Select your active theme
3. Open footer.php
4. Paste this code before the closing </body> tag
5. Click 'Update File'
-->
`,
    wix: `<!-- Wix Installation Instructions:
1. Open your Wix editor
2. Click Add → More → HTML Code
3. Paste this code into the HTML element
4. Position the element anywhere on your page
5. Publish your site
-->
`,
    squarespace: `<!-- Squarespace Installation Instructions:
1. Go to Settings → Advanced → Code Injection
2. Paste this code in the Footer section
3. Click Save
4. The widget will appear on all pages
-->
`,
    shopify: `<!-- Shopify Installation Instructions:
1. Go to Online Store → Themes
2. Click Actions → Edit Code
3. Open theme.liquid
4. Paste this code before the closing </body> tag
5. Save the file
-->
`,
    webflow: `<!-- Webflow Installation Instructions:
1. Open your Webflow project
2. Go to Project Settings → Custom Code
3. Paste this code in the Footer Code section
4. Publish your site
-->
`
  };

  return instructions[platform] || `<!-- Installation Instructions:
1. Paste this code into your website's HTML
2. Place it before the closing </body> tag
3. Save and publish your changes
-->
`;
};

/**
 * Generate platform-specific embed code
 */
export const generatePlatformEmbedCode = (
  options: WidgetOptions,
  platform: string
): string => {
  return generateEnhancedEmbedCode(
    options,
    { format: 'script-tag', async: true, defer: true },
    { platform, includeInstructions: true, wrapInComments: true }
  );
};

/**
 * Generate quick installation code (minimal configuration)
 */
export const generateQuickEmbedCode = (venueId: string): string => {
  return `<script src="https://cdn.evoque.digital/widget/v2/loader.js"
        data-venue-id="${venueId}"
        async defer>
</script>`;
};

/**
 * Generates a code snippet for using the widget with npm
 *
 * @param options Widget configuration options
 * @returns JavaScript code snippet as a string
 */
export const generateNpmCodeSnippet = (options: WidgetOptions): string => {
  // Create a clean options object with only the non-default values
  const cleanOptions: Partial<WidgetOptions> = {};

  // Always include venueId
  cleanOptions.venueId = options.venueId;

  // Only include other options if they differ from defaults
  if (options.primaryColor !== '#6366F1') {
    cleanOptions.primaryColor = options.primaryColor;
  }

  if (options.textColor !== '#FFFFFF') {
    cleanOptions.textColor = options.textColor;
  }

  if (options.position !== 'bottom-right') {
    cleanOptions.position = options.position;
  }

  if (options.welcomeMessage !== 'Hello! How can I help you with your wedding venue inquiry?') {
    cleanOptions.welcomeMessage = options.welcomeMessage;
  }

  if (options.fontFamily !== 'Inter, system-ui, sans-serif') {
    cleanOptions.fontFamily = options.fontFamily;
  }

  if (options.fontSize !== '16px') {
    cleanOptions.fontSize = options.fontSize;
  }

  if (options.buttonIcon !== 'chat') {
    cleanOptions.buttonIcon = options.buttonIcon;
  }

  if (options.showBranding !== true) {
    cleanOptions.showBranding = options.showBranding;
  }

  if (options.mobileBreakpoint !== 768) {
    cleanOptions.mobileBreakpoint = options.mobileBreakpoint;
  }

  if (options.socketUrl !== 'https://api.evoque.digital') {
    cleanOptions.socketUrl = options.socketUrl;
  }

  // Format the options object as a string with proper indentation
  const optionsString = JSON.stringify(cleanOptions, null, 2);

  // Generate the npm code snippet
  return `import EvoqueWidget from 'evoque-widget';

// Initialize the widget
const widget = new EvoqueWidget(${optionsString});

// You can also use these methods:
// widget.open();
// widget.close();
// widget.updateOptions({ primaryColor: '#FF5733' });`;
};

/**
 * Generate test embed code with debug mode enabled
 */
export const generateTestEmbedCode = (options: WidgetOptions): string => {
  return generateEnhancedEmbedCode(
    options,
    { debug: true, fallbackMode: false },
    { includeInstructions: false }
  );
};

/**
 * Validate embed code configuration
 */
export const validateEmbedConfig = (options: WidgetOptions): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!options.venueId || options.venueId.trim() === '') {
    errors.push('Venue ID is required');
  }

  if (options.venueId && options.venueId.length < 3) {
    errors.push('Venue ID must be at least 3 characters long');
  }

  if (!options.primaryColor || !options.primaryColor.match(/^#[0-9A-F]{6}$/i)) {
    errors.push('Primary color must be a valid hex color (e.g., #6366F1)');
  }

  if (!options.textColor || !options.textColor.match(/^#[0-9A-F]{6}$/i)) {
    errors.push('Text color must be a valid hex color (e.g., #FFFFFF)');
  }

  if (!['bottom-right', 'bottom-left', 'top-right', 'top-left'].includes(options.position)) {
    errors.push('Position must be one of: bottom-right, bottom-left, top-right, top-left');
  }

  if (!options.welcomeMessage || options.welcomeMessage.trim() === '') {
    errors.push('Welcome message is required');
  }

  if (options.welcomeMessage && options.welcomeMessage.length > 200) {
    errors.push('Welcome message must be 200 characters or less');
  }

  return {
    valid: errors.length === 0,
    errors
  };
};