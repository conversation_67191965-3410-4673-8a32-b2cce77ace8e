import React, { useState, useEffect, useMemo } from 'react';
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  AreaChart,
  Area,
} from 'recharts';

interface LiveActivity {
  id: string;
  type: 'inquiry' | 'lead_update' | 'booking' | 'response';
  title: string;
  description: string;
  timestamp: Date;
  priority: 'low' | 'medium' | 'high';
  data?: any;
}

interface RealTimeChartProps {
  data: LiveActivity[];
  isLive: boolean;
  timeWindow?: number; // minutes
  chartType?: 'line' | 'area';
}

interface ChartDataPoint {
  time: string;
  timestamp: number;
  inquiries: number;
  responses: number;
  bookings: number;
  totalActivity: number;
}

/**
 * Real-time chart showing live activity metrics
 */
const RealTimeChart: React.FC<RealTimeChartProps> = ({
  data,
  isLive,
  timeWindow = 60, // 1 hour default
  chartType = 'area'
}) => {
  const [chartData, setChartData] = useState<ChartDataPoint[]>([]);
  const [currentTime, setCurrentTime] = useState(new Date());

  // Update current time every minute when live
  useEffect(() => {
    if (!isLive) return;

    const interval = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [isLive]);

  // Process activity data into chart format
  const processedData = useMemo(() => {
    const now = currentTime;
    const windowStart = new Date(now.getTime() - timeWindow * 60 * 1000);
    
    // Create time slots (5-minute intervals)
    const timeSlots: ChartDataPoint[] = [];
    const slotDuration = 5 * 60 * 1000; // 5 minutes in milliseconds
    
    for (let time = windowStart.getTime(); time <= now.getTime(); time += slotDuration) {
      const slotTime = new Date(time);
      timeSlots.push({
        time: slotTime.toLocaleTimeString('en-US', { 
          hour: '2-digit', 
          minute: '2-digit',
          hour12: false 
        }),
        timestamp: time,
        inquiries: 0,
        responses: 0,
        bookings: 0,
        totalActivity: 0
      });
    }
    
    // Aggregate activities into time slots
    data.forEach(activity => {
      const activityTime = activity.timestamp.getTime();
      
      // Find the appropriate time slot
      const slotIndex = Math.floor((activityTime - windowStart.getTime()) / slotDuration);
      
      if (slotIndex >= 0 && slotIndex < timeSlots.length) {
        const slot = timeSlots[slotIndex];
        
        switch (activity.type) {
          case 'inquiry':
            slot.inquiries++;
            break;
          case 'response':
            slot.responses++;
            break;
          case 'booking':
            slot.bookings++;
            break;
        }
        
        slot.totalActivity++;
      }
    });
    
    return timeSlots;
  }, [data, currentTime, timeWindow]);

  // Update chart data when processed data changes
  useEffect(() => {
    setChartData(processedData);
  }, [processedData]);

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900">{`Time: ${label}`}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }} className="text-sm">
              {`${entry.dataKey}: ${entry.value}`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  const maxValue = Math.max(
    ...chartData.map(d => Math.max(d.inquiries, d.responses, d.bookings, d.totalActivity))
  );

  if (chartData.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center text-gray-500">
        <div className="text-center">
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-32 mx-auto mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-24 mx-auto"></div>
          </div>
          <p className="mt-4 text-sm">Loading real-time data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Chart Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className={`w-2 h-2 rounded-full ${isLive ? 'bg-green-500 animate-pulse' : 'bg-gray-400'}`}></div>
          <span className="text-sm text-gray-600">
            {isLive ? 'Live' : 'Paused'} • Last {timeWindow}m
          </span>
        </div>
        
        {/* Legend */}
        <div className="flex items-center space-x-4 text-xs">
          <div className="flex items-center space-x-1">
            <div className="w-3 h-3 bg-blue-500 rounded"></div>
            <span>Inquiries</span>
          </div>
          <div className="flex items-center space-x-1">
            <div className="w-3 h-3 bg-green-500 rounded"></div>
            <span>Responses</span>
          </div>
          <div className="flex items-center space-x-1">
            <div className="w-3 h-3 bg-purple-500 rounded"></div>
            <span>Bookings</span>
          </div>
        </div>
      </div>

      {/* Chart */}
      <div className="h-48">
        <ResponsiveContainer width="100%" height="100%">
          {chartType === 'area' ? (
            <AreaChart data={chartData} margin={{ top: 5, right: 5, left: 5, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis 
                dataKey="time" 
                tick={{ fontSize: 10 }}
                interval="preserveStartEnd"
              />
              <YAxis 
                tick={{ fontSize: 10 }}
                domain={[0, maxValue + 1]}
              />
              <Tooltip content={<CustomTooltip />} />
              
              <Area
                type="monotone"
                dataKey="inquiries"
                stackId="1"
                stroke="#3b82f6"
                fill="#3b82f6"
                fillOpacity={0.6}
              />
              <Area
                type="monotone"
                dataKey="responses"
                stackId="1"
                stroke="#10b981"
                fill="#10b981"
                fillOpacity={0.6}
              />
              <Area
                type="monotone"
                dataKey="bookings"
                stackId="1"
                stroke="#8b5cf6"
                fill="#8b5cf6"
                fillOpacity={0.6}
              />
            </AreaChart>
          ) : (
            <LineChart data={chartData} margin={{ top: 5, right: 5, left: 5, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis 
                dataKey="time" 
                tick={{ fontSize: 10 }}
                interval="preserveStartEnd"
              />
              <YAxis 
                tick={{ fontSize: 10 }}
                domain={[0, maxValue + 1]}
              />
              <Tooltip content={<CustomTooltip />} />
              
              <Line
                type="monotone"
                dataKey="inquiries"
                stroke="#3b82f6"
                strokeWidth={2}
                dot={{ r: 3 }}
                activeDot={{ r: 5 }}
              />
              <Line
                type="monotone"
                dataKey="responses"
                stroke="#10b981"
                strokeWidth={2}
                dot={{ r: 3 }}
                activeDot={{ r: 5 }}
              />
              <Line
                type="monotone"
                dataKey="bookings"
                stroke="#8b5cf6"
                strokeWidth={2}
                dot={{ r: 3 }}
                activeDot={{ r: 5 }}
              />
            </LineChart>
          )}
        </ResponsiveContainer>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-3 gap-4 pt-4 border-t border-gray-200">
        <div className="text-center">
          <div className="text-lg font-semibold text-blue-600">
            {chartData.reduce((sum, d) => sum + d.inquiries, 0)}
          </div>
          <div className="text-xs text-gray-500">Inquiries</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-semibold text-green-600">
            {chartData.reduce((sum, d) => sum + d.responses, 0)}
          </div>
          <div className="text-xs text-gray-500">Responses</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-semibold text-purple-600">
            {chartData.reduce((sum, d) => sum + d.bookings, 0)}
          </div>
          <div className="text-xs text-gray-500">Bookings</div>
        </div>
      </div>

      {/* Activity Rate */}
      <div className="text-center pt-2">
        <div className="text-sm text-gray-600">
          Activity Rate: {' '}
          <span className="font-medium">
            {(chartData.reduce((sum, d) => sum + d.totalActivity, 0) / (timeWindow / 5)).toFixed(1)}
          </span>
          {' '} events per 5min
        </div>
      </div>
    </div>
  );
};

export default RealTimeChart;
