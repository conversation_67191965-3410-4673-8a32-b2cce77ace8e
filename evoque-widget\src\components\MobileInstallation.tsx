import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { WidgetOptions } from '../types';
import { generateEmbedCode } from '../utils/embedCode';
import { generateMobileInstructions } from '../utils/qrCode';

interface MobileInstallationProps {
  config: WidgetOptions;
  platform?: string;
  onComplete?: () => void;
}

/**
 * Mobile-optimized installation interface
 * Designed for easy installation on mobile devices
 */
const MobileInstallation: React.FC<MobileInstallationProps> = ({ 
  config, 
  platform = 'general',
  onComplete 
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [embedCode, setEmbedCode] = useState('');
  const [copied, setCopied] = useState(false);
  const [instructions, setInstructions] = useState<string[]>([]);

  useEffect(() => {
    setEmbedCode(generateEmbedCode(config));
    setInstructions(generateMobileInstructions(platform));
  }, [config, platform]);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(embedCode);
      setCopied(true);
      setTimeout(() => setCopied(false), 3000);
    } catch (err) {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = embedCode;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      setCopied(true);
      setTimeout(() => setCopied(false), 3000);
    }
  };

  const handleNext = () => {
    if (currentStep < instructions.length) {
      setCurrentStep(currentStep + 1);
    } else {
      onComplete?.();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  return (
    <MobileContainer>
      <Header>
        <Title>Widget Installation</Title>
        <Subtitle>Add chat widget to your website</Subtitle>
        <ProgressBar>
          <ProgressFill progress={(currentStep / (instructions.length + 1)) * 100} />
        </ProgressBar>
      </Header>

      <Content>
        {currentStep === 0 ? (
          <CodeStep>
            <StepTitle>Step 1: Copy Installation Code</StepTitle>
            <StepDescription>
              Copy this code and paste it into your website
            </StepDescription>
            
            <CodeContainer>
              <CodeBlock>{embedCode}</CodeBlock>
              <CopyButton onClick={handleCopy} copied={copied}>
                {copied ? '✓ Copied!' : 'Copy Code'}
              </CopyButton>
            </CodeContainer>

            <WidgetPreview>
              <PreviewTitle>Preview</PreviewTitle>
              <PreviewDescription>
                Your widget will look like this:
              </PreviewDescription>
              <PreviewWidget color={config.primaryColor}>
                <WidgetIcon>{getIconForType(config.buttonIcon)}</WidgetIcon>
              </PreviewWidget>
            </WidgetPreview>
          </CodeStep>
        ) : (
          <InstructionStep>
            <StepTitle>Step {currentStep + 1}: {getPlatformName(platform)}</StepTitle>
            <StepDescription>
              {instructions[currentStep - 1]}
            </StepDescription>
            
            <InstructionDetails>
              <DetailIcon>📱</DetailIcon>
              <DetailText>
                Follow this step on your website's admin panel
              </DetailText>
            </InstructionDetails>

            {currentStep === instructions.length && (
              <CompletionMessage>
                <CompletionIcon>🎉</CompletionIcon>
                <CompletionTitle>Installation Complete!</CompletionTitle>
                <CompletionText>
                  Your chat widget is now live on your website. 
                  Visitors can start chatting with you right away.
                </CompletionText>
              </CompletionMessage>
            )}
          </InstructionStep>
        )}
      </Content>

      <Footer>
        <NavigationButtons>
          <NavButton 
            variant="secondary" 
            onClick={handlePrevious}
            disabled={currentStep === 0}
          >
            Previous
          </NavButton>
          <NavButton 
            variant="primary" 
            onClick={handleNext}
          >
            {currentStep === instructions.length ? 'Finish' : 'Next'}
          </NavButton>
        </NavigationButtons>

        <HelpSection>
          <HelpText>Need help?</HelpText>
          <HelpLink href="https://help.evoque.digital/widget-installation" target="_blank">
            View detailed guide
          </HelpLink>
        </HelpSection>
      </Footer>
    </MobileContainer>
  );
};

// Helper functions
const getIconForType = (type: string): string => {
  const icons = {
    chat: '💬',
    message: '✉️',
    question: '❓',
    heart: '💖'
  };
  return icons[type] || '💬';
};

const getPlatformName = (platform: string): string => {
  const names = {
    wordpress: 'WordPress Setup',
    wix: 'Wix Setup',
    squarespace: 'Squarespace Setup',
    shopify: 'Shopify Setup',
    webflow: 'Webflow Setup',
    general: 'Website Setup'
  };
  return names[platform] || 'Website Setup';
};

// Styled Components
const MobileContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
`;

const Header = styled.div`
  padding: 20px;
  text-align: center;
  color: white;
`;

const Title = styled.h1`
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
`;

const Subtitle = styled.p`
  margin: 0 0 20px 0;
  font-size: 16px;
  opacity: 0.9;
`;

const ProgressBar = styled.div`
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  overflow: hidden;
`;

const ProgressFill = styled.div<{ progress: number }>`
  width: ${props => props.progress}%;
  height: 100%;
  background: white;
  transition: width 0.3s ease;
`;

const Content = styled.div`
  flex: 1;
  background: white;
  border-radius: 20px 20px 0 0;
  padding: 24px;
  margin-top: 20px;
`;

const CodeStep = styled.div``;

const InstructionStep = styled.div``;

const StepTitle = styled.h2`
  margin: 0 0 12px 0;
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
`;

const StepDescription = styled.p`
  margin: 0 0 24px 0;
  font-size: 16px;
  color: #6b7280;
  line-height: 1.5;
`;

const CodeContainer = styled.div`
  margin-bottom: 32px;
`;

const CodeBlock = styled.div`
  background: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 16px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  color: #374151;
  word-break: break-all;
  margin-bottom: 16px;
  max-height: 120px;
  overflow-y: auto;
`;

const CopyButton = styled.button<{ copied: boolean }>`
  width: 100%;
  padding: 16px;
  background: ${props => props.copied ? '#10b981' : '#6366f1'};
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  
  &:active {
    transform: scale(0.98);
  }
`;

const WidgetPreview = styled.div`
  text-align: center;
`;

const PreviewTitle = styled.h3`
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
`;

const PreviewDescription = styled.p`
  margin: 0 0 20px 0;
  font-size: 14px;
  color: #6b7280;
`;

const PreviewWidget = styled.div<{ color: string }>`
  width: 60px;
  height: 60px;
  background: ${props => props.color};
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  animation: pulse 2s infinite;
  
  @keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
  }
`;

const WidgetIcon = styled.div`
  font-size: 24px;
`;

const InstructionDetails = styled.div`
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 24px;
`;

const DetailIcon = styled.div`
  font-size: 20px;
`;

const DetailText = styled.div`
  font-size: 14px;
  color: #0369a1;
  line-height: 1.4;
`;

const CompletionMessage = styled.div`
  text-align: center;
  padding: 24px;
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 12px;
`;

const CompletionIcon = styled.div`
  font-size: 48px;
  margin-bottom: 16px;
`;

const CompletionTitle = styled.h3`
  margin: 0 0 12px 0;
  font-size: 20px;
  font-weight: 600;
  color: #166534;
`;

const CompletionText = styled.p`
  margin: 0;
  font-size: 16px;
  color: #166534;
  line-height: 1.5;
`;

const Footer = styled.div`
  padding: 24px;
  background: white;
`;

const NavigationButtons = styled.div`
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
`;

const NavButton = styled.button<{ variant: 'primary' | 'secondary'; disabled?: boolean }>`
  flex: 1;
  padding: 16px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  
  ${props => props.variant === 'primary' ? `
    background: #6366f1;
    color: white;
    border: none;
    
    &:active {
      background: #5856eb;
      transform: scale(0.98);
    }
  ` : `
    background: #f9fafb;
    color: #374151;
    border: 1px solid #e5e7eb;
    
    &:active {
      background: #f3f4f6;
      transform: scale(0.98);
    }
  `}
  
  ${props => props.disabled && `
    opacity: 0.5;
    cursor: not-allowed;
    
    &:active {
      transform: none;
    }
  `}
`;

const HelpSection = styled.div`
  text-align: center;
`;

const HelpText = styled.span`
  font-size: 14px;
  color: #6b7280;
  margin-right: 8px;
`;

const HelpLink = styled.a`
  font-size: 14px;
  color: #6366f1;
  text-decoration: none;
  font-weight: 500;
  
  &:hover {
    text-decoration: underline;
  }
`;

export default MobileInstallation;
