#!/usr/bin/env ts-node

/**
 * Deployment Validation Script
 * 
 * Comprehensive end-to-end validation of the deployed Evoque platform.
 * Tests all critical user journeys and system integrations.
 */

import { PrismaClient } from '@prisma/client';
import fetch from 'node-fetch';
import WebSocket from 'ws';

const prisma = new PrismaClient();

interface ValidationResult {
  test: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
  duration?: number;
  details?: any;
}

interface ValidationReport {
  overall: 'pass' | 'fail' | 'warning';
  timestamp: string;
  environment: string;
  results: ValidationResult[];
  summary: {
    passed: number;
    failed: number;
    warnings: number;
    total: number;
  };
}

/**
 * Test widget installation flow
 */
async function testWidgetInstallation(): Promise<ValidationResult[]> {
  const results: ValidationResult[] = [];
  const startTime = Date.now();
  
  try {
    // Test widget loader availability
    const loaderUrl = process.env.WIDGET_CDN_URL 
      ? `${process.env.WIDGET_CDN_URL}/loader.js`
      : 'https://cdn.evoque.digital/widget/v2/loader.js';
    
    const loaderResponse = await fetch(loaderUrl, { timeout: 5000 });
    
    if (loaderResponse.ok) {
      results.push({
        test: 'Widget Loader Availability',
        status: 'pass',
        message: 'Widget loader script accessible from CDN',
        duration: Date.now() - startTime
      });
    } else {
      results.push({
        test: 'Widget Loader Availability',
        status: 'fail',
        message: `Widget loader returned ${loaderResponse.status}`,
        duration: Date.now() - startTime
      });
    }
    
    // Test widget iframe fallback
    const iframeUrl = process.env.WIDGET_CDN_URL 
      ? `${process.env.WIDGET_CDN_URL}/iframe.html`
      : 'https://cdn.evoque.digital/widget/v2/iframe.html';
    
    const iframeResponse = await fetch(iframeUrl, { timeout: 5000 });
    
    if (iframeResponse.ok) {
      results.push({
        test: 'Widget Iframe Fallback',
        status: 'pass',
        message: 'Widget iframe fallback available',
        duration: Date.now() - startTime
      });
    } else {
      results.push({
        test: 'Widget Iframe Fallback',
        status: 'fail',
        message: `Widget iframe returned ${iframeResponse.status}`,
        duration: Date.now() - startTime
      });
    }
    
    // Test installation examples
    const exampleUrl = process.env.WIDGET_CDN_URL 
      ? `${process.env.WIDGET_CDN_URL}/examples/basic.html`
      : 'https://cdn.evoque.digital/widget/v2/examples/basic.html';
    
    const exampleResponse = await fetch(exampleUrl, { timeout: 5000 });
    
    if (exampleResponse.ok) {
      results.push({
        test: 'Installation Examples',
        status: 'pass',
        message: 'Installation examples accessible',
        duration: Date.now() - startTime
      });
    } else {
      results.push({
        test: 'Installation Examples',
        status: 'warning',
        message: 'Installation examples not available',
        duration: Date.now() - startTime
      });
    }
    
  } catch (error) {
    results.push({
      test: 'Widget Installation Flow',
      status: 'fail',
      message: `Widget installation test failed: ${error.message}`,
      duration: Date.now() - startTime
    });
  }
  
  return results;
}

/**
 * Test GraphQL API functionality
 */
async function testGraphQLAPI(): Promise<ValidationResult[]> {
  const results: ValidationResult[] = [];
  const apiUrl = process.env.NEXT_PUBLIC_GRAPHQL_URL || 'http://localhost:4000/graphql';
  
  // Test basic connectivity
  const startTime = Date.now();
  try {
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ query: '{ __typename }' }),
      timeout: 5000
    });
    
    if (response.ok) {
      results.push({
        test: 'GraphQL Basic Connectivity',
        status: 'pass',
        message: 'GraphQL API responding',
        duration: Date.now() - startTime
      });
    } else {
      results.push({
        test: 'GraphQL Basic Connectivity',
        status: 'fail',
        message: `GraphQL API returned ${response.status}`,
        duration: Date.now() - startTime
      });
    }
  } catch (error) {
    results.push({
      test: 'GraphQL Basic Connectivity',
      status: 'fail',
      message: `GraphQL API connection failed: ${error.message}`,
      duration: Date.now() - startTime
    });
  }
  
  // Test analytics resolvers
  try {
    const analyticsQuery = `
      query TestAnalytics {
        analyticsTypes {
          id
          name
          description
        }
      }
    `;
    
    const analyticsStartTime = Date.now();
    const analyticsResponse = await fetch(apiUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ query: analyticsQuery }),
      timeout: 5000
    });
    
    if (analyticsResponse.ok) {
      const data = await analyticsResponse.json();
      if (data.data && data.data.analyticsTypes) {
        results.push({
          test: 'Analytics Resolvers',
          status: 'pass',
          message: `Analytics resolvers working (${data.data.analyticsTypes.length} types)`,
          duration: Date.now() - analyticsStartTime
        });
      } else {
        results.push({
          test: 'Analytics Resolvers',
          status: 'fail',
          message: 'Analytics resolvers returned no data',
          duration: Date.now() - analyticsStartTime
        });
      }
    } else {
      results.push({
        test: 'Analytics Resolvers',
        status: 'fail',
        message: `Analytics query failed with ${analyticsResponse.status}`,
        duration: Date.now() - analyticsStartTime
      });
    }
  } catch (error) {
    results.push({
      test: 'Analytics Resolvers',
      status: 'fail',
      message: `Analytics test failed: ${error.message}`
    });
  }
  
  return results;
}

/**
 * Test WebSocket connectivity
 */
async function testWebSocketConnection(): Promise<ValidationResult> {
  const startTime = Date.now();
  
  return new Promise((resolve) => {
    try {
      const wsUrl = process.env.NEXT_PUBLIC_GRAPHQL_WS_URL || 'ws://localhost:4000/graphql';
      const ws = new WebSocket(wsUrl, 'graphql-ws');
      
      const timeout = setTimeout(() => {
        ws.close();
        resolve({
          test: 'WebSocket Connection',
          status: 'fail',
          message: 'WebSocket connection timeout',
          duration: Date.now() - startTime
        });
      }, 5000);
      
      ws.on('open', () => {
        clearTimeout(timeout);
        ws.close();
        resolve({
          test: 'WebSocket Connection',
          status: 'pass',
          message: 'WebSocket connection successful',
          duration: Date.now() - startTime
        });
      });
      
      ws.on('error', (error) => {
        clearTimeout(timeout);
        resolve({
          test: 'WebSocket Connection',
          status: 'fail',
          message: `WebSocket connection failed: ${error.message}`,
          duration: Date.now() - startTime
        });
      });
      
    } catch (error) {
      resolve({
        test: 'WebSocket Connection',
        status: 'fail',
        message: `WebSocket test failed: ${error.message}`,
        duration: Date.now() - startTime
      });
    }
  });
}

/**
 * Test database operations
 */
async function testDatabaseOperations(): Promise<ValidationResult[]> {
  const results: ValidationResult[] = [];
  
  // Test basic connectivity
  const connectStartTime = Date.now();
  try {
    await prisma.$connect();
    results.push({
      test: 'Database Connectivity',
      status: 'pass',
      message: 'Database connection successful',
      duration: Date.now() - connectStartTime
    });
  } catch (error) {
    results.push({
      test: 'Database Connectivity',
      status: 'fail',
      message: `Database connection failed: ${error.message}`,
      duration: Date.now() - connectStartTime
    });
    return results; // Skip other tests if connection fails
  }
  
  // Test data integrity
  try {
    const analyticsTypesCount = await prisma.analyticsType.count();
    const leadStatusesCount = await prisma.leadStatus.count();
    const inquirySourcesCount = await prisma.inquirySource.count();
    
    if (analyticsTypesCount > 0 && leadStatusesCount > 0 && inquirySourcesCount > 0) {
      results.push({
        test: 'Database Seed Data',
        status: 'pass',
        message: `Seed data present (${analyticsTypesCount} analytics types, ${leadStatusesCount} lead statuses, ${inquirySourcesCount} inquiry sources)`,
        details: { analyticsTypesCount, leadStatusesCount, inquirySourcesCount }
      });
    } else {
      results.push({
        test: 'Database Seed Data',
        status: 'fail',
        message: 'Missing seed data in database',
        details: { analyticsTypesCount, leadStatusesCount, inquirySourcesCount }
      });
    }
  } catch (error) {
    results.push({
      test: 'Database Seed Data',
      status: 'fail',
      message: `Database seed data check failed: ${error.message}`
    });
  }
  
  // Test write operations
  try {
    const testVenue = await prisma.venue.create({
      data: {
        name: 'Test Venue - Deployment Validation',
        slug: `test-venue-${Date.now()}`,
        email: '<EMAIL>',
        phone: '+1234567890',
        address: '123 Test Street',
        city: 'Test City',
        state: 'TS',
        zipCode: '12345',
        country: 'US',
        isActive: false // Mark as test data
      }
    });
    
    // Clean up test data
    await prisma.venue.delete({ where: { id: testVenue.id } });
    
    results.push({
      test: 'Database Write Operations',
      status: 'pass',
      message: 'Database write operations working correctly'
    });
  } catch (error) {
    results.push({
      test: 'Database Write Operations',
      status: 'fail',
      message: `Database write test failed: ${error.message}`
    });
  }
  
  return results;
}

/**
 * Test notification services
 */
async function testNotificationServices(): Promise<ValidationResult[]> {
  const results: ValidationResult[] = [];
  
  // Test email service (Resend)
  if (process.env.RESEND_API_KEY) {
    try {
      const response = await fetch('https://api.resend.com/domains', {
        headers: { 'Authorization': `Bearer ${process.env.RESEND_API_KEY}` },
        timeout: 5000
      });
      
      if (response.ok) {
        results.push({
          test: 'Email Service (Resend)',
          status: 'pass',
          message: 'Resend email service accessible'
        });
      } else {
        results.push({
          test: 'Email Service (Resend)',
          status: 'fail',
          message: `Resend API returned ${response.status}`
        });
      }
    } catch (error) {
      results.push({
        test: 'Email Service (Resend)',
        status: 'fail',
        message: `Resend test failed: ${error.message}`
      });
    }
  } else {
    results.push({
      test: 'Email Service (Resend)',
      status: 'warning',
      message: 'Resend API key not configured'
    });
  }
  
  // Test SMS service (Twilio)
  if (process.env.TWILIO_ACCOUNT_SID && process.env.TWILIO_AUTH_TOKEN) {
    try {
      const auth = Buffer.from(`${process.env.TWILIO_ACCOUNT_SID}:${process.env.TWILIO_AUTH_TOKEN}`).toString('base64');
      const response = await fetch(`https://api.twilio.com/2010-04-01/Accounts/${process.env.TWILIO_ACCOUNT_SID}.json`, {
        headers: { 'Authorization': `Basic ${auth}` },
        timeout: 5000
      });
      
      if (response.ok) {
        results.push({
          test: 'SMS Service (Twilio)',
          status: 'pass',
          message: 'Twilio SMS service accessible'
        });
      } else {
        results.push({
          test: 'SMS Service (Twilio)',
          status: 'fail',
          message: `Twilio API returned ${response.status}`
        });
      }
    } catch (error) {
      results.push({
        test: 'SMS Service (Twilio)',
        status: 'fail',
        message: `Twilio test failed: ${error.message}`
      });
    }
  } else {
    results.push({
      test: 'SMS Service (Twilio)',
      status: 'warning',
      message: 'Twilio credentials not configured'
    });
  }
  
  // Test push notification service (Firebase)
  if (process.env.FIREBASE_SERVER_KEY) {
    try {
      const response = await fetch('https://fcm.googleapis.com/fcm/send', {
        method: 'POST',
        headers: {
          'Authorization': `key=${process.env.FIREBASE_SERVER_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          to: 'test-token',
          notification: { title: 'Test', body: 'Deployment validation' }
        }),
        timeout: 5000
      });
      
      // FCM returns 400 for invalid token, which is expected
      if (response.status === 400 || response.status === 200) {
        results.push({
          test: 'Push Notification Service (Firebase)',
          status: 'pass',
          message: 'Firebase FCM service accessible'
        });
      } else {
        results.push({
          test: 'Push Notification Service (Firebase)',
          status: 'fail',
          message: `Firebase FCM returned ${response.status}`
        });
      }
    } catch (error) {
      results.push({
        test: 'Push Notification Service (Firebase)',
        status: 'fail',
        message: `Firebase test failed: ${error.message}`
      });
    }
  } else {
    results.push({
      test: 'Push Notification Service (Firebase)',
      status: 'warning',
      message: 'Firebase server key not configured'
    });
  }
  
  return results;
}

/**
 * Run comprehensive deployment validation
 */
async function runDeploymentValidation(): Promise<ValidationReport> {
  console.log('🔍 Running deployment validation...\n');
  
  const allResults: ValidationResult[] = [];
  
  // Run all test suites
  const [
    widgetResults,
    graphqlResults,
    wsResult,
    dbResults,
    notificationResults
  ] = await Promise.all([
    testWidgetInstallation(),
    testGraphQLAPI(),
    testWebSocketConnection(),
    testDatabaseOperations(),
    testNotificationServices()
  ]);
  
  allResults.push(...widgetResults);
  allResults.push(...graphqlResults);
  allResults.push(wsResult);
  allResults.push(...dbResults);
  allResults.push(...notificationResults);
  
  // Calculate summary
  const summary = {
    passed: allResults.filter(r => r.status === 'pass').length,
    failed: allResults.filter(r => r.status === 'fail').length,
    warnings: allResults.filter(r => r.status === 'warning').length,
    total: allResults.length
  };
  
  // Determine overall status
  let overall: 'pass' | 'fail' | 'warning' = 'pass';
  if (summary.failed > 0) {
    overall = 'fail';
  } else if (summary.warnings > 0) {
    overall = 'warning';
  }
  
  const report: ValidationReport = {
    overall,
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    results: allResults,
    summary
  };
  
  return report;
}

/**
 * Display validation report
 */
function displayValidationReport(report: ValidationReport): void {
  console.log('📊 Deployment Validation Report');
  console.log('='.repeat(60));
  console.log(`Overall Status: ${getStatusEmoji(report.overall)} ${report.overall.toUpperCase()}`);
  console.log(`Environment: ${report.environment}`);
  console.log(`Timestamp: ${report.timestamp}`);
  console.log(`Summary: ${report.summary.passed} passed, ${report.summary.failed} failed, ${report.summary.warnings} warnings\n`);
  
  // Group results by status
  const failedTests = report.results.filter(r => r.status === 'fail');
  const warningTests = report.results.filter(r => r.status === 'warning');
  const passedTests = report.results.filter(r => r.status === 'pass');
  
  if (failedTests.length > 0) {
    console.log('❌ FAILED TESTS:');
    failedTests.forEach(test => {
      console.log(`   ${test.test}: ${test.message}`);
      if (test.duration) console.log(`      Duration: ${test.duration}ms`);
    });
    console.log();
  }
  
  if (warningTests.length > 0) {
    console.log('⚠️  WARNINGS:');
    warningTests.forEach(test => {
      console.log(`   ${test.test}: ${test.message}`);
      if (test.duration) console.log(`      Duration: ${test.duration}ms`);
    });
    console.log();
  }
  
  if (passedTests.length > 0) {
    console.log('✅ PASSED TESTS:');
    passedTests.forEach(test => {
      console.log(`   ${test.test}: ${test.message}`);
      if (test.duration) console.log(`      Duration: ${test.duration}ms`);
    });
  }
  
  console.log('\n' + '='.repeat(60));
  
  if (report.overall === 'pass') {
    console.log('🎉 Deployment validation PASSED! Platform is ready for production.');
  } else if (report.overall === 'warning') {
    console.log('⚠️  Deployment validation passed with WARNINGS. Review warnings before proceeding.');
  } else {
    console.log('❌ Deployment validation FAILED. Fix critical issues before deploying to production.');
  }
}

function getStatusEmoji(status: string): string {
  switch (status) {
    case 'pass': return '✅';
    case 'warning': return '⚠️';
    case 'fail': return '❌';
    default: return '❓';
  }
}

// CLI interface
if (require.main === module) {
  const args = process.argv.slice(2);
  const jsonOutput = args.includes('--json');
  const exitOnFailure = args.includes('--exit-on-failure');
  
  runDeploymentValidation()
    .then(report => {
      if (jsonOutput) {
        console.log(JSON.stringify(report, null, 2));
      } else {
        displayValidationReport(report);
      }
      
      if (exitOnFailure && report.overall === 'fail') {
        process.exit(1);
      } else {
        process.exit(0);
      }
    })
    .catch(error => {
      console.error('💥 Deployment validation failed:', error);
      process.exit(1);
    })
    .finally(() => {
      prisma.$disconnect();
    });
}

export { runDeploymentValidation, ValidationReport, ValidationResult };
