import { WidgetOptions } from '../types';

/**
 * QR Code generation utilities for mobile widget installation
 */

interface QRCodeOptions {
  size?: number;
  format?: 'png' | 'svg' | 'jpeg';
  errorCorrectionLevel?: 'L' | 'M' | 'Q' | 'H';
  margin?: number;
  color?: string;
  backgroundColor?: string;
}

interface MobileInstallationData {
  venueId: string;
  primaryColor?: string;
  position?: string;
  welcomeMessage?: string;
  installationUrl: string;
  qrCodeUrl: string;
}

/**
 * Generate QR code URL for widget installation
 */
export const generateQRCode = (
  data: string, 
  options: QRCodeOptions = {}
): string => {
  const {
    size = 200,
    format = 'png',
    errorCorrectionLevel = 'M',
    margin = 10,
    color = '000000',
    backgroundColor = 'ffffff'
  } = options;

  // Using QR Server API (free service)
  const params = new URLSearchParams({
    size: `${size}x${size}`,
    data: data,
    format: format,
    ecc: errorCorrectionLevel,
    margin: margin.toString(),
    color: color,
    bgcolor: backgroundColor
  });

  return `https://api.qrserver.com/v1/create-qr-code/?${params.toString()}`;
};

/**
 * Generate mobile installation URL with widget configuration
 */
export const generateMobileInstallationUrl = (
  config: WidgetOptions,
  baseUrl: string = 'https://widget.evoque.digital'
): string => {
  const params = new URLSearchParams({
    venueId: config.venueId,
    primaryColor: config.primaryColor,
    textColor: config.textColor,
    position: config.position,
    welcomeMessage: config.welcomeMessage,
    fontFamily: config.fontFamily,
    fontSize: config.fontSize,
    buttonIcon: config.buttonIcon,
    showBranding: config.showBranding.toString(),
    mobileBreakpoint: config.mobileBreakpoint.toString()
  });

  return `${baseUrl}/mobile-install?${params.toString()}`;
};

/**
 * Generate complete mobile installation data including QR code
 */
export const generateMobileInstallationData = (
  config: WidgetOptions,
  qrOptions: QRCodeOptions = {}
): MobileInstallationData => {
  const installationUrl = generateMobileInstallationUrl(config);
  const qrCodeUrl = generateQRCode(installationUrl, qrOptions);

  return {
    venueId: config.venueId,
    primaryColor: config.primaryColor,
    position: config.position,
    welcomeMessage: config.welcomeMessage,
    installationUrl,
    qrCodeUrl
  };
};

/**
 * Generate QR code for venue-specific installation
 */
export const generateVenueQRCode = (
  venueId: string,
  customization: Partial<WidgetOptions> = {},
  qrOptions: QRCodeOptions = {}
): string => {
  const config: WidgetOptions = {
    venueId,
    primaryColor: '#6366F1',
    textColor: '#FFFFFF',
    position: 'bottom-right',
    welcomeMessage: 'Hello! How can I help you with your wedding venue inquiry?',
    fontFamily: 'Inter, system-ui, sans-serif',
    fontSize: '16px',
    buttonIcon: 'chat',
    showBranding: true,
    mobileBreakpoint: 768,
    socketUrl: 'https://api.evoque.digital',
    ...customization
  };

  const installationUrl = generateMobileInstallationUrl(config);
  return generateQRCode(installationUrl, qrOptions);
};

/**
 * Generate QR code for quick installation with minimal config
 */
export const generateQuickInstallQRCode = (
  venueId: string,
  qrOptions: QRCodeOptions = {}
): string => {
  const quickInstallUrl = `https://widget.evoque.digital/quick-install/${venueId}`;
  return generateQRCode(quickInstallUrl, qrOptions);
};

/**
 * Generate QR code with custom styling for branding
 */
export const generateBrandedQRCode = (
  data: string,
  brandColor: string = '#6366F1',
  size: number = 200
): string => {
  // Remove # from color if present
  const color = brandColor.replace('#', '');
  
  return generateQRCode(data, {
    size,
    color,
    backgroundColor: 'ffffff',
    errorCorrectionLevel: 'M',
    margin: 10
  });
};

/**
 * Validate QR code data length (QR codes have size limits)
 */
export const validateQRCodeData = (data: string): { valid: boolean; message?: string } => {
  // QR code capacity varies by error correction level and version
  // For safety, we'll use conservative limits
  const maxLength = 2000; // Conservative limit for most QR readers
  
  if (data.length > maxLength) {
    return {
      valid: false,
      message: `Data too long for QR code (${data.length} chars, max ${maxLength})`
    };
  }
  
  return { valid: true };
};

/**
 * Generate installation instructions for mobile users
 */
export const generateMobileInstructions = (platform: string): string[] => {
  const instructions = {
    wordpress: [
      "1. Open your WordPress admin on mobile",
      "2. Go to Appearance → Theme Editor",
      "3. Edit footer.php",
      "4. Paste the widget code before </body>",
      "5. Save changes"
    ],
    wix: [
      "1. Open Wix Editor on mobile",
      "2. Add → More → HTML Code",
      "3. Paste the widget code",
      "4. Position and publish"
    ],
    squarespace: [
      "1. Go to Settings → Advanced",
      "2. Select Code Injection",
      "3. Paste code in Footer section",
      "4. Save changes"
    ],
    general: [
      "1. Access your website's admin panel",
      "2. Find the theme or template editor",
      "3. Locate the footer or layout file",
      "4. Paste the widget code before </body>",
      "5. Save and publish changes"
    ]
  };

  return instructions[platform] || instructions.general;
};

/**
 * Generate shareable QR code with installation guide
 */
export const generateShareableInstallation = (
  config: WidgetOptions,
  platform: string = 'general'
): {
  qrCodeUrl: string;
  installationUrl: string;
  instructions: string[];
  embedCode: string;
} => {
  const installationUrl = generateMobileInstallationUrl(config);
  const qrCodeUrl = generateBrandedQRCode(installationUrl, config.primaryColor);
  const instructions = generateMobileInstructions(platform);
  
  // Generate embed code
  const embedCode = `<script src="https://cdn.evoque.digital/widget/v2/loader.js" 
    data-venue-id="${config.venueId}"
    data-primary-color="${config.primaryColor}"
    data-position="${config.position}"
    data-welcome-message="${config.welcomeMessage}"
    async defer>
</script>`;

  return {
    qrCodeUrl,
    installationUrl,
    instructions,
    embedCode
  };
};

/**
 * Generate QR code for testing widget installation
 */
export const generateTestQRCode = (
  venueId: string,
  testMode: boolean = true
): string => {
  const testUrl = `https://widget.evoque.digital/test?venueId=${venueId}&mode=${testMode ? 'test' : 'live'}`;
  return generateQRCode(testUrl, {
    size: 150,
    errorCorrectionLevel: 'H', // Higher error correction for testing
    color: testMode ? 'ff6b35' : '10b981' // Orange for test, green for live
  });
};

/**
 * Batch generate QR codes for multiple venues
 */
export const batchGenerateQRCodes = (
  venues: Array<{ id: string; name: string; config?: Partial<WidgetOptions> }>,
  qrOptions: QRCodeOptions = {}
): Array<{ venueId: string; venueName: string; qrCodeUrl: string; installationUrl: string }> => {
  return venues.map(venue => {
    const config: WidgetOptions = {
      venueId: venue.id,
      primaryColor: '#6366F1',
      textColor: '#FFFFFF',
      position: 'bottom-right',
      welcomeMessage: `Hello! Welcome to ${venue.name}. How can I help you?`,
      fontFamily: 'Inter, system-ui, sans-serif',
      fontSize: '16px',
      buttonIcon: 'chat',
      showBranding: true,
      mobileBreakpoint: 768,
      socketUrl: 'https://api.evoque.digital',
      ...venue.config
    };

    const installationUrl = generateMobileInstallationUrl(config);
    const qrCodeUrl = generateQRCode(installationUrl, qrOptions);

    return {
      venueId: venue.id,
      venueName: venue.name,
      qrCodeUrl,
      installationUrl
    };
  });
};

/**
 * Generate QR code with analytics tracking
 */
export const generateTrackableQRCode = (
  config: WidgetOptions,
  source: string = 'qr-code',
  campaign: string = 'widget-installation'
): string => {
  const baseUrl = generateMobileInstallationUrl(config);
  const trackingParams = new URLSearchParams({
    utm_source: source,
    utm_medium: 'qr-code',
    utm_campaign: campaign,
    utm_content: config.venueId
  });

  const trackableUrl = `${baseUrl}&${trackingParams.toString()}`;
  return generateQRCode(trackableUrl);
};

export default {
  generateQRCode,
  generateMobileInstallationUrl,
  generateMobileInstallationData,
  generateVenueQRCode,
  generateQuickInstallQRCode,
  generateBrandedQRCode,
  validateQRCodeData,
  generateMobileInstructions,
  generateShareableInstallation,
  generateTestQRCode,
  batchGenerateQRCodes,
  generateTrackableQRCode
};
