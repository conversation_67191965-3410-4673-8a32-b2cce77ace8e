import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';

/**
 * Analytics Service
 * Handles real-time analytics tracking and calculations for venue performance metrics
 */
export class AnalyticsService {
  private static prisma = new PrismaClient();

  // Cache analytics types to avoid repeated database queries
  private static analyticsTypeCache = new Map<string, string>();

  /**
   * Track an analytics event with improved performance and error handling
   */
  public static async trackEvent(
    venueId: string,
    eventType: string,
    data: Record<string, any> = {},
    userId?: string,
    entityType?: string,
    entityId?: string
  ): Promise<void> {
    try {
      // Get analytics type ID from cache or database
      let typeId = this.analyticsTypeCache.get(eventType);

      if (!typeId) {
        const analyticsType = await this.prisma.analyticsType.findFirst({
          where: { name: eventType },
          select: { id: true },
        });

        if (!analyticsType) {
          logger.warn(`Analytics type not found: ${eventType}`);
          return;
        }

        typeId = analyticsType.id;
        this.analyticsTypeCache.set(eventType, typeId);
      }

      // Create the analytics event
      await this.prisma.analyticsEvent.create({
        data: {
          venueId,
          typeId,
          userId,
          entityType,
          entityId,
          data,
          occurredAt: new Date(),
        },
      });

      logger.debug(`Analytics event tracked: ${eventType} for venue ${venueId}`);
    } catch (error) {
      logger.error('Error tracking analytics event:', error);
      // Don't throw - analytics failures shouldn't break the main flow
    }
  }

  /**
   * Calculate response time between inquiry and first response
   */
  public static async calculateResponseTime(inquiryId: string): Promise<number | null> {
    try {
      const inquiry = await this.prisma.inquiry.findUnique({
        where: { id: inquiryId },
        include: {
          messages: {
            orderBy: { createdAt: 'asc' },
            take: 2, // First user message and first venue response
          },
        },
      });

      if (!inquiry || inquiry.messages.length < 2) {
        return null;
      }

      const firstUserMessage = inquiry.messages.find(m => m.senderType === 'user');
      const firstVenueResponse = inquiry.messages.find(m => m.senderType === 'venue');

      if (!firstUserMessage || !firstVenueResponse) {
        return null;
      }

      // Calculate response time in minutes
      const responseTimeMs = firstVenueResponse.createdAt.getTime() - firstUserMessage.createdAt.getTime();
      const responseTimeMinutes = responseTimeMs / (1000 * 60);

      // Track the response time
      await this.trackEvent(
        inquiry.venueId,
        'response-time',
        {
          inquiryId,
          responseTimeMinutes,
          responseTimeMs,
        },
        firstVenueResponse.senderId || undefined,
        'inquiry',
        inquiryId
      );

      return responseTimeMinutes;
    } catch (error) {
      logger.error('Error calculating response time:', error);
      return null;
    }
  }

  /**
   * Track source attribution for inquiries
   */
  public static async trackSourceAttribution(
    venueId: string,
    source: string,
    inquiryId?: string,
    additionalData: any = {}
  ): Promise<void> {
    await this.trackEvent(
      venueId,
      'source-attribution',
      {
        source,
        inquiryId,
        ...additionalData,
      },
      undefined,
      inquiryId ? 'inquiry' : undefined,
      inquiryId
    );
  }

  /**
   * Track lead stage progression
   */
  public static async trackLeadStageChange(
    leadId: string,
    fromStage: string | null,
    toStage: string,
    userId?: string
  ): Promise<void> {
    try {
      const lead = await this.prisma.lead.findUnique({
        where: { id: leadId },
        select: { venueId: true },
      });

      if (!lead) {
        return;
      }

      await this.trackEvent(
        lead.venueId,
        'lead-stage-changed',
        {
          leadId,
          fromStage,
          toStage,
          timestamp: new Date(),
        },
        userId,
        'lead',
        leadId
      );
    } catch (error) {
      logger.error('Error tracking lead stage change:', error);
    }
  }

  /**
   * Calculate conversion rates for a venue with optimized database queries
   */
  public static async calculateConversionRates(
    venueId: string,
    startDate: Date,
    endDate: Date
  ): Promise<{
    inquiryToLeadRate: number;
    leadToBookingRate: number;
    overallConversionRate: number;
  }> {
    try {
      const dateFilter = { gte: startDate, lte: endDate };

      // Use Promise.all for parallel queries to improve performance
      const [inquiryCount, inquiryWithLeadCount, leadCount, wonLeadCount] = await Promise.all([
        // Total inquiries
        this.prisma.inquiry.count({
          where: { venueId, createdAt: dateFilter },
        }),

        // Inquiries that became leads
        this.prisma.inquiry.count({
          where: {
            venueId,
            createdAt: dateFilter,
            lead: { isNot: null },
          },
        }),

        // Total leads
        this.prisma.lead.count({
          where: { venueId, createdAt: dateFilter },
        }),

        // Won leads (bookings)
        this.prisma.lead.count({
          where: {
            venueId,
            createdAt: dateFilter,
            stage: 'won',
          },
        }),
      ]);

      // Calculate rates with safe division
      const inquiryToLeadRate = inquiryCount > 0 ? (inquiryWithLeadCount / inquiryCount) * 100 : 0;
      const leadToBookingRate = leadCount > 0 ? (wonLeadCount / leadCount) * 100 : 0;
      const overallConversionRate = inquiryCount > 0 ? (wonLeadCount / inquiryCount) * 100 : 0;

      return {
        inquiryToLeadRate: Math.round(inquiryToLeadRate * 100) / 100, // Round to 2 decimal places
        leadToBookingRate: Math.round(leadToBookingRate * 100) / 100,
        overallConversionRate: Math.round(overallConversionRate * 100) / 100,
      };
    } catch (error) {
      logger.error('Error calculating conversion rates:', error);
      return {
        inquiryToLeadRate: 0,
        leadToBookingRate: 0,
        overallConversionRate: 0,
      };
    }
  }

  /**
   * Get average response time for a venue
   */
  public static async getAverageResponseTime(
    venueId: string,
    startDate: Date,
    endDate: Date
  ): Promise<number | null> {
    try {
      const responseTimeEvents = await this.prisma.analyticsEvent.findMany({
        where: {
          venueId,
          type: {
            name: 'response-time',
          },
          occurredAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });

      if (responseTimeEvents.length === 0) {
        return null;
      }

      const totalResponseTime = responseTimeEvents.reduce((sum, event) => {
        const responseTime = (event.data as any)?.responseTimeMinutes || 0;
        return sum + responseTime;
      }, 0);

      return totalResponseTime / responseTimeEvents.length;
    } catch (error) {
      logger.error('Error calculating average response time:', error);
      return null;
    }
  }

  /**
   * Track revenue metrics
   */
  public static async trackRevenue(
    venueId: string,
    amount: number,
    type: 'booking' | 'payment' | 'cancellation',
    leadId?: string,
    bookingId?: string
  ): Promise<void> {
    const eventType = type === 'booking' ? 'booking-created' : 
                     type === 'payment' ? 'payment-received' : 'booking-cancelled';

    await this.trackEvent(
      venueId,
      eventType,
      {
        amount,
        leadId,
        bookingId,
        currency: 'USD', // This could be configurable
      },
      undefined,
      leadId ? 'lead' : 'booking',
      leadId || bookingId
    );
  }

  /**
   * Get revenue metrics for a venue
   */
  public static async getRevenueMetrics(
    venueId: string,
    startDate: Date,
    endDate: Date
  ): Promise<{
    totalRevenue: number;
    bookingCount: number;
    averageBookingValue: number;
    cancellationRate: number;
  }> {
    try {
      const revenueEvents = await this.prisma.analyticsEvent.findMany({
        where: {
          venueId,
          type: {
            category: 'revenue',
          },
          occurredAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        include: {
          type: true,
        },
      });

      let totalRevenue = 0;
      let bookingCount = 0;
      let cancellationCount = 0;

      revenueEvents.forEach(event => {
        const amount = (event.data as any)?.amount || 0;
        
        if (event.type.name === 'booking-created' || event.type.name === 'payment-received') {
          totalRevenue += amount;
          if (event.type.name === 'booking-created') {
            bookingCount++;
          }
        } else if (event.type.name === 'booking-cancelled') {
          cancellationCount++;
        }
      });

      const averageBookingValue = bookingCount > 0 ? totalRevenue / bookingCount : 0;
      const cancellationRate = bookingCount > 0 ? (cancellationCount / bookingCount) * 100 : 0;

      return {
        totalRevenue,
        bookingCount,
        averageBookingValue,
        cancellationRate,
      };
    } catch (error) {
      logger.error('Error calculating revenue metrics:', error);
      return {
        totalRevenue: 0,
        bookingCount: 0,
        averageBookingValue: 0,
        cancellationRate: 0,
      };
    }
  }

  /**
   * Get top traffic sources for a venue
   */
  public static async getTopSources(
    venueId: string,
    startDate: Date,
    endDate: Date,
    limit: number = 10
  ): Promise<Array<{ source: string; count: number; percentage: number }>> {
    try {
      const sourceEvents = await this.prisma.analyticsEvent.findMany({
        where: {
          venueId,
          type: {
            name: 'source-attribution',
          },
          occurredAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });

      const sourceCounts = sourceEvents.reduce((acc, event) => {
        const source = (event.data as any)?.source || 'unknown';
        acc[source] = (acc[source] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const total = sourceEvents.length;
      const sources = Object.entries(sourceCounts)
        .map(([source, count]) => ({
          source,
          count,
          percentage: total > 0 ? (count / total) * 100 : 0,
        }))
        .sort((a, b) => b.count - a.count)
        .slice(0, limit);

      return sources;
    } catch (error) {
      logger.error('Error getting top sources:', error);
      return [];
    }
  }

  /**
   * Clean up old analytics events (for data retention)
   */
  public static async cleanupOldEvents(retentionDays: number = 365): Promise<void> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

      const deletedCount = await this.prisma.analyticsEvent.deleteMany({
        where: {
          occurredAt: {
            lt: cutoffDate,
          },
        },
      });

      logger.info(`Cleaned up ${deletedCount.count} old analytics events`);
    } catch (error) {
      logger.error('Error cleaning up old analytics events:', error);
    }
  }
}
