# Evoque Platform - Production Deployment Checklist

## 🚀 Pre-Deployment Preparation

### 1. Environment Setup
- [ ] **Production Database**
  - [ ] PostgreSQL instance configured
  - [ ] Connection string in environment variables
  - [ ] Database backup strategy in place
  - [ ] SSL/TLS encryption enabled

- [ ] **Environment Variables**
  ```bash
  DATABASE_URL=postgresql://...
  JWT_SECRET=your-secure-jwt-secret
  NODE_ENV=production
  PORT=3000
  CORS_ORIGIN=https://yourdomain.com
  ```

- [ ] **CDN Configuration**
  - [ ] CloudFlare or AWS CloudFront setup
  - [ ] Widget loader script deployment location
  - [ ] SSL certificate for widget domain
  - [ ] Cache headers configured

### 2. Database Migration
- [ ] **Run Analytics Types Seeding**
  ```bash
  cd evoque-api
  npm run seed:analytics-types
  ```

- [ ] **Verify Database Schema**
  ```bash
  npx prisma db push
  npx prisma generate
  ```

- [ ] **Test Database Connection**
  ```bash
  npm run test:db-connection
  ```

### 3. Build and Test
- [ ] **API Build**
  ```bash
  cd evoque-api
  npm install
  npm run build
  npm run test
  ```

- [ ] **Widget Build**
  ```bash
  cd evoque-widget
  npm install
  npm run build
  npm run build:loader
  ```

- [ ] **Run Validation Scripts**
  ```bash
  cd evoque-api
  npm run validate:implementation
  npm run test:analytics
  ```

---

## 🔧 Deployment Steps

### 1. API Deployment
- [ ] **Deploy API Server**
  - [ ] Upload built files to server
  - [ ] Install production dependencies
  - [ ] Configure process manager (PM2)
  - [ ] Set up reverse proxy (Nginx)
  - [ ] Configure SSL certificate

- [ ] **Health Check**
  ```bash
  curl https://api.yourdomain.com/health
  # Should return: {"status":"ok"}
  ```

- [ ] **GraphQL Endpoint Test**
  ```bash
  curl -X POST https://api.yourdomain.com/graphql \
    -H "Content-Type: application/json" \
    -d '{"query":"query { analyticsTypes { id name } }"}'
  ```

### 2. Widget Deployment
- [ ] **Deploy Widget Loader**
  - [ ] Upload `loader.js` to CDN
  - [ ] Upload `iframe.html` to CDN
  - [ ] Configure CORS headers
  - [ ] Test CDN accessibility

- [ ] **Widget Endpoint Test**
  ```bash
  curl https://cdn.yourdomain.com/widget/v2/loader.js
  # Should return JavaScript code
  ```

### 3. Platform Files Deployment
- [ ] **WordPress Plugin**
  - [ ] Package plugin files
  - [ ] Test on WordPress test site
  - [ ] Prepare for WordPress.org submission

- [ ] **Platform Guides**
  - [ ] Upload installation guides to documentation site
  - [ ] Create video tutorials
  - [ ] Test installation procedures

---

## ✅ Post-Deployment Verification

### 1. Functional Testing
- [ ] **Analytics Dashboard**
  - [ ] Login to admin dashboard
  - [ ] Verify analytics data loads (not mock data)
  - [ ] Test all dashboard features
  - [ ] Check real-time updates

- [ ] **Widget Installation**
  - [ ] Test on WordPress site
  - [ ] Test on Wix site
  - [ ] Test on Squarespace site
  - [ ] Verify mobile responsiveness

- [ ] **API Endpoints**
  - [ ] Test venue detection: `/api/widget/detect`
  - [ ] Test widget config: `/api/widget/config/:venueId`
  - [ ] Test analytics tracking: `/api/widget/installation`
  - [ ] Test message handling: `/api/widget/message`

### 2. Performance Testing
- [ ] **Load Times**
  - [ ] Widget loads in <2 seconds
  - [ ] API responses in <500ms
  - [ ] Dashboard loads in <3 seconds
  - [ ] Mobile performance acceptable

- [ ] **Stress Testing**
  - [ ] Multiple concurrent widget loads
  - [ ] High volume of analytics events
  - [ ] Database performance under load
  - [ ] CDN performance globally

### 3. Security Verification
- [ ] **HTTPS Everywhere**
  - [ ] API endpoints use HTTPS
  - [ ] Widget loader uses HTTPS
  - [ ] No mixed content warnings

- [ ] **CORS Configuration**
  - [ ] Widget works from any domain
  - [ ] API accepts requests from widget
  - [ ] No unauthorized access possible

- [ ] **Data Protection**
  - [ ] Personal data encrypted
  - [ ] Database access restricted
  - [ ] API authentication working

---

## 📊 Monitoring Setup

### 1. Error Tracking
- [ ] **Sentry Configuration**
  - [ ] API error tracking enabled
  - [ ] Widget error tracking enabled
  - [ ] Alert notifications configured
  - [ ] Error reporting tested

### 2. Performance Monitoring
- [ ] **Application Monitoring**
  - [ ] Server resource monitoring
  - [ ] Database performance tracking
  - [ ] API response time monitoring
  - [ ] Widget load time tracking

### 3. Analytics Monitoring
- [ ] **Usage Analytics**
  - [ ] Widget installation tracking
  - [ ] User interaction analytics
  - [ ] Conversion funnel tracking
  - [ ] Platform usage statistics

---

## 🎯 Go-Live Checklist

### 1. Final Verification
- [ ] **All Tests Passing**
  - [ ] Automated test suite passes
  - [ ] Manual testing complete
  - [ ] Performance benchmarks met
  - [ ] Security scan clean

- [ ] **Documentation Ready**
  - [ ] Installation guides published
  - [ ] API documentation updated
  - [ ] Support documentation ready
  - [ ] Video tutorials available

### 2. Support Preparation
- [ ] **Help Desk Setup**
  - [ ] Support ticket system configured
  - [ ] FAQ documentation ready
  - [ ] Support team trained
  - [ ] Escalation procedures defined

- [ ] **Monitoring Alerts**
  - [ ] Error rate alerts configured
  - [ ] Performance degradation alerts
  - [ ] Uptime monitoring active
  - [ ] On-call rotation established

### 3. Launch Communication
- [ ] **Internal Team**
  - [ ] Development team notified
  - [ ] Support team ready
  - [ ] Management informed
  - [ ] Rollback plan prepared

- [ ] **External Communication**
  - [ ] Venue partners notified
  - [ ] Installation guides shared
  - [ ] Support channels announced
  - [ ] Feedback collection ready

---

## 🚨 Emergency Procedures

### Rollback Plan
1. **Immediate Actions**
   - [ ] Revert API deployment
   - [ ] Restore previous widget version
   - [ ] Notify affected venues
   - [ ] Document issues encountered

2. **Investigation**
   - [ ] Analyze error logs
   - [ ] Identify root cause
   - [ ] Develop fix plan
   - [ ] Test fix thoroughly

### Support Escalation
1. **Level 1**: Installation issues → Documentation/FAQ
2. **Level 2**: Technical issues → Support team
3. **Level 3**: Critical bugs → Development team
4. **Level 4**: System outage → Emergency response

---

## 📋 Success Criteria

### Technical Metrics
- [ ] **Uptime**: >99.5%
- [ ] **Performance**: Widget loads <2s, API responds <500ms
- [ ] **Error Rate**: <1% of requests
- [ ] **Installation Success**: >95% success rate

### Business Metrics
- [ ] **Installation Time**: <5 minutes average
- [ ] **Support Tickets**: <10% of installations need support
- [ ] **Platform Coverage**: Works on 95% of venue websites
- [ ] **User Satisfaction**: >4.5/5 rating

---

## 🎉 Launch Day Protocol

### T-24 Hours
- [ ] Final system checks
- [ ] Team availability confirmed
- [ ] Monitoring systems active
- [ ] Rollback plan reviewed

### T-4 Hours
- [ ] Final deployment
- [ ] Smoke tests complete
- [ ] All systems green
- [ ] Team on standby

### T-0 (Launch)
- [ ] Go-live announcement
- [ ] Monitor all systems
- [ ] Track key metrics
- [ ] Respond to issues immediately

### T+24 Hours
- [ ] System stability confirmed
- [ ] Performance metrics reviewed
- [ ] Support ticket analysis
- [ ] Success metrics evaluated

---

**Deployment Lead**: [Name]  
**Date**: [Date]  
**Status**: [ ] Ready for Production

**Sign-off Required**:
- [ ] Technical Lead
- [ ] Product Manager  
- [ ] DevOps Engineer
- [ ] QA Lead
