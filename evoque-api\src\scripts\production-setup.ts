#!/usr/bin/env ts-node

/**
 * Production Setup Script
 * 
 * This script handles the complete setup of the Evoque platform for production:
 * - Database migrations
 * - Initial data seeding
 * - Environment validation
 * - Health checks
 */

import { PrismaClient } from '@prisma/client';
import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

const prisma = new PrismaClient();

interface SetupConfig {
  skipMigrations?: boolean;
  skipSeeding?: boolean;
  validateOnly?: boolean;
  verbose?: boolean;
}

/**
 * Validate all required environment variables
 */
async function validateEnvironment(): Promise<boolean> {
  console.log('🔍 Validating environment configuration...');
  
  const requiredVars = [
    'DATABASE_URL',
    'JWT_SECRET',
    'NODE_ENV',
    'PORT',
    'CORS_ORIGIN'
  ];

  const optionalVars = [
    'FIREBASE_SERVER_KEY',
    'TWILIO_ACCOUNT_SID',
    'TWILIO_AUTH_TOKEN',
    'RESEND_API_KEY'
  ];

  let isValid = true;
  const missing: string[] = [];
  const warnings: string[] = [];

  // Check required variables
  for (const varName of requiredVars) {
    if (!process.env[varName]) {
      missing.push(varName);
      isValid = false;
    }
  }

  // Check optional variables (warnings only)
  for (const varName of optionalVars) {
    if (!process.env[varName]) {
      warnings.push(varName);
    }
  }

  if (missing.length > 0) {
    console.error('❌ Missing required environment variables:');
    missing.forEach(varName => console.error(`   - ${varName}`));
  }

  if (warnings.length > 0) {
    console.warn('⚠️  Missing optional environment variables (some features may not work):');
    warnings.forEach(varName => console.warn(`   - ${varName}`));
  }

  // Validate specific formats
  if (process.env.DATABASE_URL && !process.env.DATABASE_URL.startsWith('postgresql://')) {
    console.error('❌ DATABASE_URL must be a PostgreSQL connection string');
    isValid = false;
  }

  if (process.env.JWT_SECRET && process.env.JWT_SECRET.length < 32) {
    console.error('❌ JWT_SECRET must be at least 32 characters long');
    isValid = false;
  }

  if (process.env.PORT && isNaN(parseInt(process.env.PORT))) {
    console.error('❌ PORT must be a valid number');
    isValid = false;
  }

  if (isValid) {
    console.log('✅ Environment validation passed');
  }

  return isValid;
}

/**
 * Test database connection
 */
async function testDatabaseConnection(): Promise<boolean> {
  console.log('🔍 Testing database connection...');
  
  try {
    await prisma.$connect();
    await prisma.$queryRaw`SELECT 1`;
    console.log('✅ Database connection successful');
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    return false;
  }
}

/**
 * Run database migrations
 */
async function runMigrations(): Promise<boolean> {
  console.log('🔄 Running database migrations...');
  
  try {
    execSync('npx prisma migrate deploy', { 
      stdio: 'inherit',
      cwd: process.cwd()
    });
    console.log('✅ Database migrations completed');
    return true;
  } catch (error) {
    console.error('❌ Database migrations failed:', error.message);
    return false;
  }
}

/**
 * Generate Prisma client
 */
async function generatePrismaClient(): Promise<boolean> {
  console.log('🔄 Generating Prisma client...');
  
  try {
    execSync('npx prisma generate', { 
      stdio: 'inherit',
      cwd: process.cwd()
    });
    console.log('✅ Prisma client generated');
    return true;
  } catch (error) {
    console.error('❌ Prisma client generation failed:', error.message);
    return false;
  }
}

/**
 * Seed initial data
 */
async function seedDatabase(): Promise<boolean> {
  console.log('🌱 Seeding database with initial data...');
  
  try {
    // Seed analytics types
    const analyticsTypes = [
      { name: 'page_view', description: 'Page view tracking' },
      { name: 'widget_load', description: 'Widget load event' },
      { name: 'chat_started', description: 'Chat conversation started' },
      { name: 'inquiry_submitted', description: 'Inquiry form submitted' },
      { name: 'lead_qualified', description: 'Lead qualification completed' },
      { name: 'booking_request', description: 'Booking request submitted' },
      { name: 'tour_scheduled', description: 'Venue tour scheduled' },
      { name: 'contract_signed', description: 'Contract signed' },
      { name: 'payment_received', description: 'Payment received' },
      { name: 'event_completed', description: 'Event completed' }
    ];

    for (const type of analyticsTypes) {
      await prisma.analyticsType.upsert({
        where: { name: type.name },
        update: {},
        create: type
      });
    }

    // Seed lead statuses
    const leadStatuses = [
      { name: 'new', description: 'New lead', order: 1 },
      { name: 'contacted', description: 'Initial contact made', order: 2 },
      { name: 'qualified', description: 'Lead qualified', order: 3 },
      { name: 'proposal_sent', description: 'Proposal sent', order: 4 },
      { name: 'negotiating', description: 'In negotiation', order: 5 },
      { name: 'won', description: 'Deal won', order: 6 },
      { name: 'lost', description: 'Deal lost', order: 7 }
    ];

    for (const status of leadStatuses) {
      await prisma.leadStatus.upsert({
        where: { name: status.name },
        update: {},
        create: status
      });
    }

    // Seed inquiry sources
    const inquirySources = [
      { name: 'widget_chat', description: 'Chat widget' },
      { name: 'contact_form', description: 'Contact form' },
      { name: 'phone_call', description: 'Phone call' },
      { name: 'email', description: 'Email inquiry' },
      { name: 'social_media', description: 'Social media' },
      { name: 'referral', description: 'Referral' },
      { name: 'wedding_show', description: 'Wedding show' },
      { name: 'website_direct', description: 'Direct website visit' }
    ];

    for (const source of inquirySources) {
      await prisma.inquirySource.upsert({
        where: { name: source.name },
        update: {},
        create: source
      });
    }

    console.log('✅ Database seeding completed');
    return true;
  } catch (error) {
    console.error('❌ Database seeding failed:', error.message);
    return false;
  }
}

/**
 * Validate database schema
 */
async function validateSchema(): Promise<boolean> {
  console.log('🔍 Validating database schema...');
  
  try {
    // Test that all main tables exist and are accessible
    const tests = [
      prisma.user.count(),
      prisma.venue.count(),
      prisma.inquiry.count(),
      prisma.lead.count(),
      prisma.analyticsEvent.count(),
      prisma.knowledgeBaseItem.count()
    ];

    await Promise.all(tests);
    console.log('✅ Database schema validation passed');
    return true;
  } catch (error) {
    console.error('❌ Database schema validation failed:', error.message);
    return false;
  }
}

/**
 * Run health checks
 */
async function runHealthChecks(): Promise<boolean> {
  console.log('🏥 Running health checks...');
  
  try {
    // Check if server can start (basic syntax check)
    const serverPath = path.join(process.cwd(), 'src', 'index.ts');
    if (!fs.existsSync(serverPath)) {
      throw new Error('Server entry point not found');
    }

    // Check if all required modules can be imported
    const requiredModules = [
      './graphql/schema',
      './graphql/resolvers',
      './services/websocket',
      './services/notifications'
    ];

    for (const modulePath of requiredModules) {
      try {
        require(modulePath);
      } catch (error) {
        throw new Error(`Failed to import ${modulePath}: ${error.message}`);
      }
    }

    console.log('✅ Health checks passed');
    return true;
  } catch (error) {
    console.error('❌ Health checks failed:', error.message);
    return false;
  }
}

/**
 * Main setup function
 */
async function setupProduction(config: SetupConfig = {}): Promise<boolean> {
  console.log('🚀 Starting Evoque Platform Production Setup\n');
  
  let success = true;

  // Step 1: Validate environment
  if (!await validateEnvironment()) {
    success = false;
  }

  if (config.validateOnly) {
    return success;
  }

  // Step 2: Test database connection
  if (!await testDatabaseConnection()) {
    success = false;
  }

  // Step 3: Generate Prisma client
  if (!config.skipMigrations && !await generatePrismaClient()) {
    success = false;
  }

  // Step 4: Run migrations
  if (!config.skipMigrations && !await runMigrations()) {
    success = false;
  }

  // Step 5: Validate schema
  if (!await validateSchema()) {
    success = false;
  }

  // Step 6: Seed database
  if (!config.skipSeeding && !await seedDatabase()) {
    success = false;
  }

  // Step 7: Run health checks
  if (!await runHealthChecks()) {
    success = false;
  }

  if (success) {
    console.log('\n🎉 Production setup completed successfully!');
    console.log('\nNext steps:');
    console.log('1. Start the API server: npm start');
    console.log('2. Deploy the dashboard: cd ../evoque-dashboard && npm run build');
    console.log('3. Deploy widget to CDN: npm run cdn:deploy');
    console.log('4. Run end-to-end tests: npm run validate');
  } else {
    console.log('\n❌ Production setup failed. Please fix the errors above and try again.');
  }

  return success;
}

// CLI interface
if (require.main === module) {
  const args = process.argv.slice(2);
  const config: SetupConfig = {
    skipMigrations: args.includes('--skip-migrations'),
    skipSeeding: args.includes('--skip-seeding'),
    validateOnly: args.includes('--validate-only'),
    verbose: args.includes('--verbose')
  };

  setupProduction(config)
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('💥 Setup failed with error:', error);
      process.exit(1);
    })
    .finally(() => {
      prisma.$disconnect();
    });
}

export { setupProduction, validateEnvironment, testDatabaseConnection };
