import React, { useState } from 'react';
import styled from 'styled-components';
import { WidgetOptions } from '../types';
import { generatePlatformEmbedCode, generateQuickEmbedCode } from '../utils/embedCode';
import { generateVenueQRCode } from '../utils/qrCode';

interface InstallationGuideProps {
  venueId: string;
  config?: Partial<WidgetOptions>;
  onClose?: () => void;
}

interface PlatformGuide {
  id: string;
  name: string;
  icon: string;
  description: string;
  difficulty: 'Easy' | 'Medium' | 'Advanced';
  steps: string[];
  videoUrl?: string;
  docsUrl?: string;
}

/**
 * Comprehensive installation guide for all platforms
 */
const InstallationGuide: React.FC<InstallationGuideProps> = ({ venueId, config = {}, onClose }) => {
  const [selectedPlatform, setSelectedPlatform] = useState<string>('');
  const [showCode, setShowCode] = useState(false);
  const [copied, setCopied] = useState(false);

  const defaultConfig: WidgetOptions = {
    venueId,
    primaryColor: '#6366F1',
    textColor: '#FFFFFF',
    position: 'bottom-right',
    welcomeMessage: 'Hello! How can I help you with your wedding venue inquiry?',
    fontFamily: 'Inter, system-ui, sans-serif',
    fontSize: '16px',
    buttonIcon: 'chat',
    showBranding: true,
    mobileBreakpoint: 768,
    socketUrl: 'https://api.evoque.digital',
    ...config
  };

  const platforms: PlatformGuide[] = [
    {
      id: 'wordpress',
      name: 'WordPress',
      icon: '🔧',
      description: 'Most popular CMS platform',
      difficulty: 'Easy',
      steps: [
        'Log in to your WordPress admin dashboard',
        'Go to Appearance → Theme Editor',
        'Select your active theme',
        'Open the footer.php file',
        'Paste the widget code before the closing </body> tag',
        'Click "Update File" to save changes',
        'Visit your website to see the widget'
      ],
      videoUrl: 'https://help.evoque.digital/videos/wordpress-installation',
      docsUrl: 'https://help.evoque.digital/wordpress'
    },
    {
      id: 'wix',
      name: 'Wix',
      icon: '🎨',
      description: 'Drag-and-drop website builder',
      difficulty: 'Easy',
      steps: [
        'Open your Wix editor',
        'Click "Add" → "More" → "HTML Code"',
        'Drag the HTML element to your page',
        'Paste the widget code into the HTML element',
        'Position the element anywhere on your page',
        'Click "Publish" to make your site live'
      ],
      videoUrl: 'https://help.evoque.digital/videos/wix-installation',
      docsUrl: 'https://help.evoque.digital/wix'
    },
    {
      id: 'squarespace',
      name: 'Squarespace',
      icon: '📐',
      description: 'Professional website builder',
      difficulty: 'Easy',
      steps: [
        'Go to Settings → Advanced → Code Injection',
        'Paste the widget code in the Footer section',
        'Click "Save" to apply changes',
        'The widget will appear on all pages of your site'
      ],
      videoUrl: 'https://help.evoque.digital/videos/squarespace-installation',
      docsUrl: 'https://help.evoque.digital/squarespace'
    },
    {
      id: 'shopify',
      name: 'Shopify',
      icon: '🛒',
      description: 'E-commerce platform',
      difficulty: 'Medium',
      steps: [
        'Go to Online Store → Themes',
        'Click "Actions" → "Edit Code"',
        'Open the theme.liquid file',
        'Paste the widget code before the closing </body> tag',
        'Save the file',
        'Preview your store to see the widget'
      ],
      videoUrl: 'https://help.evoque.digital/videos/shopify-installation',
      docsUrl: 'https://help.evoque.digital/shopify'
    },
    {
      id: 'webflow',
      name: 'Webflow',
      icon: '⚡',
      description: 'Visual web development platform',
      difficulty: 'Medium',
      steps: [
        'Open your Webflow project',
        'Go to Project Settings → Custom Code',
        'Paste the widget code in the Footer Code section',
        'Publish your site to make changes live'
      ],
      videoUrl: 'https://help.evoque.digital/videos/webflow-installation',
      docsUrl: 'https://help.evoque.digital/webflow'
    },
    {
      id: 'custom',
      name: 'Custom HTML',
      icon: '💻',
      description: 'Any website with HTML access',
      difficulty: 'Advanced',
      steps: [
        'Access your website\'s HTML files',
        'Open the main template or layout file',
        'Locate the closing </body> tag',
        'Paste the widget code just before </body>',
        'Save and upload the file to your server',
        'Clear any caches and test the widget'
      ],
      docsUrl: 'https://help.evoque.digital/custom-html'
    }
  ];

  const selectedGuide = platforms.find(p => p.id === selectedPlatform);
  const embedCode = selectedPlatform ? generatePlatformEmbedCode(defaultConfig, selectedPlatform) : generateQuickEmbedCode(venueId);
  const qrCodeUrl = generateVenueQRCode(venueId, config);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(embedCode);
      setCopied(true);
      setTimeout(() => setCopied(false), 3000);
    } catch (err) {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = embedCode;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      setCopied(true);
      setTimeout(() => setCopied(false), 3000);
    }
  };

  return (
    <GuideContainer>
      <Header>
        <CloseButton onClick={onClose}>×</CloseButton>
        <Title>Widget Installation Guide</Title>
        <Subtitle>Choose your platform and follow the step-by-step instructions</Subtitle>
      </Header>

      <Content>
        {!selectedPlatform ? (
          <PlatformSelection>
            <SectionTitle>Select Your Website Platform</SectionTitle>
            <PlatformGrid>
              {platforms.map(platform => (
                <PlatformCard
                  key={platform.id}
                  onClick={() => setSelectedPlatform(platform.id)}
                >
                  <PlatformIcon>{platform.icon}</PlatformIcon>
                  <PlatformName>{platform.name}</PlatformName>
                  <PlatformDescription>{platform.description}</PlatformDescription>
                  <DifficultyBadge difficulty={platform.difficulty}>
                    {platform.difficulty}
                  </DifficultyBadge>
                </PlatformCard>
              ))}
            </PlatformGrid>

            <QuickInstall>
              <QuickTitle>Quick Installation</QuickTitle>
              <QuickDescription>
                For developers: Use this universal code that works on any platform
              </QuickDescription>
              <QuickCode onClick={() => setShowCode(!showCode)}>
                {showCode ? 'Hide Code' : 'Show Universal Code'}
              </QuickCode>
              {showCode && (
                <CodeBlock>
                  <code>{generateQuickEmbedCode(venueId)}</code>
                  <CopyButton onClick={handleCopy}>
                    {copied ? 'Copied!' : 'Copy'}
                  </CopyButton>
                </CodeBlock>
              )}
            </QuickInstall>
          </PlatformSelection>
        ) : (
          <PlatformInstructions>
            <BackButton onClick={() => setSelectedPlatform('')}>
              ← Back to Platform Selection
            </BackButton>
            
            <PlatformHeader>
              <PlatformIcon large>{selectedGuide?.icon}</PlatformIcon>
              <div>
                <PlatformTitle>{selectedGuide?.name} Installation</PlatformTitle>
                <PlatformMeta>
                  <DifficultyBadge difficulty={selectedGuide?.difficulty || 'Easy'}>
                    {selectedGuide?.difficulty}
                  </DifficultyBadge>
                  <EstimatedTime>~5 minutes</EstimatedTime>
                </PlatformMeta>
              </div>
            </PlatformHeader>

            <InstructionSteps>
              <StepsTitle>Step-by-Step Instructions</StepsTitle>
              <StepsList>
                {selectedGuide?.steps.map((step, index) => (
                  <StepItem key={index}>
                    <StepNumber>{index + 1}</StepNumber>
                    <StepText>{step}</StepText>
                  </StepItem>
                ))}
              </StepsList>
            </InstructionSteps>

            <CodeSection>
              <CodeTitle>Installation Code</CodeTitle>
              <CodeBlock>
                <code>{embedCode}</code>
                <CopyButton onClick={handleCopy}>
                  {copied ? 'Copied!' : 'Copy Code'}
                </CopyButton>
              </CodeBlock>
            </CodeSection>

            <ResourcesSection>
              <ResourcesTitle>Additional Resources</ResourcesTitle>
              <ResourcesList>
                {selectedGuide?.videoUrl && (
                  <ResourceItem>
                    <ResourceIcon>🎥</ResourceIcon>
                    <ResourceLink href={selectedGuide.videoUrl} target="_blank">
                      Watch Video Tutorial
                    </ResourceLink>
                  </ResourceItem>
                )}
                {selectedGuide?.docsUrl && (
                  <ResourceItem>
                    <ResourceIcon>📖</ResourceIcon>
                    <ResourceLink href={selectedGuide.docsUrl} target="_blank">
                      Detailed Documentation
                    </ResourceLink>
                  </ResourceItem>
                )}
                <ResourceItem>
                  <ResourceIcon>📱</ResourceIcon>
                  <ResourceText>Mobile Installation</ResourceText>
                  <QRCode src={qrCodeUrl} alt="QR Code for mobile installation" />
                </ResourceItem>
              </ResourcesList>
            </ResourcesSection>
          </PlatformInstructions>
        )}
      </Content>

      <Footer>
        <HelpText>
          Need help? Contact our support team at{' '}
          <HelpLink href="mailto:<EMAIL>"><EMAIL></HelpLink>
        </HelpText>
      </Footer>
    </GuideContainer>
  );
};

// Styled Components
const GuideContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  padding: 20px;
`;

const Content = styled.div`
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 900px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
`;

const Header = styled.div`
  padding: 24px;
  border-bottom: 1px solid #e5e7eb;
  position: relative;
`;

const CloseButton = styled.button`
  position: absolute;
  top: 16px;
  right: 16px;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6b7280;
  
  &:hover {
    color: #374151;
  }
`;

const Title = styled.h1`
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  color: #111827;
`;

const Subtitle = styled.p`
  margin: 0;
  color: #6b7280;
  font-size: 16px;
`;

const PlatformSelection = styled.div`
  padding: 24px;
`;

const SectionTitle = styled.h2`
  margin: 0 0 24px 0;
  font-size: 20px;
  font-weight: 600;
  color: #111827;
`;

const PlatformGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 32px;
`;

const PlatformCard = styled.div`
  padding: 20px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  cursor: pointer;
  text-align: center;
  transition: all 0.2s;
  
  &:hover {
    border-color: #6366f1;
    background: #f0f9ff;
    transform: translateY(-2px);
  }
`;

const PlatformIcon = styled.div<{ large?: boolean }>`
  font-size: ${props => props.large ? '48px' : '32px'};
  margin-bottom: 12px;
`;

const PlatformName = styled.h3`
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
`;

const PlatformDescription = styled.p`
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #6b7280;
`;

const DifficultyBadge = styled.span<{ difficulty: string }>`
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  background: ${props => 
    props.difficulty === 'Easy' ? '#dcfce7' :
    props.difficulty === 'Medium' ? '#fef3c7' : '#fee2e2'
  };
  color: ${props => 
    props.difficulty === 'Easy' ? '#166534' :
    props.difficulty === 'Medium' ? '#92400e' : '#991b1b'
  };
`;

const QuickInstall = styled.div`
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
`;

const QuickTitle = styled.h3`
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
`;

const QuickDescription = styled.p`
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #6b7280;
`;

const QuickCode = styled.button`
  padding: 8px 16px;
  background: #6366f1;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  
  &:hover {
    background: #5856eb;
  }
`;

const CodeBlock = styled.div`
  background: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  margin-top: 16px;
  position: relative;
  
  code {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    line-height: 1.5;
    color: #374151;
    white-space: pre-wrap;
    word-break: break-all;
  }
`;

const CopyButton = styled.button`
  position: absolute;
  top: 8px;
  right: 8px;
  padding: 6px 12px;
  background: #10b981;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  
  &:hover {
    background: #059669;
  }
`;

const PlatformInstructions = styled.div`
  padding: 24px;
`;

const BackButton = styled.button`
  background: none;
  border: none;
  color: #6366f1;
  font-size: 14px;
  cursor: pointer;
  margin-bottom: 24px;
  
  &:hover {
    text-decoration: underline;
  }
`;

const PlatformHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 32px;
`;

const PlatformTitle = styled.h2`
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #111827;
`;

const PlatformMeta = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const EstimatedTime = styled.span`
  font-size: 14px;
  color: #6b7280;
`;

const InstructionSteps = styled.div`
  margin-bottom: 32px;
`;

const StepsTitle = styled.h3`
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
`;

const StepsList = styled.ol`
  margin: 0;
  padding: 0;
  list-style: none;
`;

const StepItem = styled.li`
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 16px;
`;

const StepNumber = styled.div`
  width: 32px;
  height: 32px;
  background: #6366f1;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  flex-shrink: 0;
`;

const StepText = styled.div`
  font-size: 16px;
  line-height: 1.5;
  color: #374151;
  padding-top: 4px;
`;

const CodeSection = styled.div`
  margin-bottom: 32px;
`;

const CodeTitle = styled.h3`
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
`;

const ResourcesSection = styled.div``;

const ResourcesTitle = styled.h3`
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
`;

const ResourcesList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const ResourceItem = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const ResourceIcon = styled.span`
  font-size: 20px;
`;

const ResourceLink = styled.a`
  color: #6366f1;
  text-decoration: none;
  font-weight: 500;
  
  &:hover {
    text-decoration: underline;
  }
`;

const ResourceText = styled.span`
  color: #374151;
  font-weight: 500;
`;

const QRCode = styled.img`
  width: 80px;
  height: 80px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-left: auto;
`;

const Footer = styled.div`
  padding: 24px;
  border-top: 1px solid #e5e7eb;
  text-align: center;
  background: #f9fafb;
`;

const HelpText = styled.p`
  margin: 0;
  font-size: 14px;
  color: #6b7280;
`;

const HelpLink = styled.a`
  color: #6366f1;
  text-decoration: none;
  
  &:hover {
    text-decoration: underline;
  }
`;

export default InstallationGuide;
